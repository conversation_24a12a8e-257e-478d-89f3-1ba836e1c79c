"""
Refactored Main script to run SPX Overnight Options Strategy Backtest
Uses the new configuration system and modular architecture
"""

import os
import sys
import argparse
from datetime import datetime
import logging

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from config_manager import ConfigurationManager, load_config
from overnight_strategy import SPXOvernightStrategy


def setup_logging(log_level: str = 'INFO') -> None:
    """
    Set up logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
    """
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('strategy.log')
        ]
    )


def parse_arguments() -> argparse.Namespace:
    """
    Parse command line arguments
    
    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(
        description='SPX Overnight Options Strategy Backtest',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                           # Run with default configuration
  python main.py --config custom.json     # Run with custom configuration file
  python main.py --start-date 2024-01-01  # Override start date
  python main.py --verbose                # Enable debug logging
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='Path to configuration file (JSON format)'
    )
    
    parser.add_argument(
        '--start-date',
        type=str,
        help='Backtest start date (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--end-date',
        type=str,
        help='Backtest end date (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--initial-capital',
        type=float,
        help='Initial capital amount'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose (debug) logging'
    )
    
    parser.add_argument(
        '--validate-config',
        action='store_true',
        help='Validate configuration and exit'
    )
    
    parser.add_argument(
        '--print-config',
        action='store_true',
        help='Print configuration and exit'
    )
    
    return parser.parse_args()


def apply_cli_overrides(config, args: argparse.Namespace) -> None:
    """
    Apply command line argument overrides to configuration
    
    Args:
        config: Configuration object to modify
        args: Parsed command line arguments
    """
    if args.start_date:
        config.strategy.start_date = args.start_date
    
    if args.end_date:
        config.strategy.end_date = args.end_date
    
    if args.initial_capital:
        config.strategy.initial_capital = args.initial_capital


def main():
    """
    Main function to run the SPX overnight options strategy backtest
    """
    # Parse command line arguments
    args = parse_arguments()
    
    # Set up logging
    log_level = 'DEBUG' if args.verbose else 'INFO'
    setup_logging(log_level)
    
    logger = logging.getLogger(__name__)
    
    try:
        print("SPX Overnight Options Strategy Backtest")
        print("=" * 50)
        
        # Load configuration
        config_manager = ConfigurationManager(args.config)
        config = config_manager.load_configuration()
        
        # Apply CLI overrides
        apply_cli_overrides(config, args)
        
        # Handle special modes
        if args.validate_config:
            errors = config.validate()
            if errors:
                print("❌ Configuration validation failed:")
                for error in errors:
                    print(f"  - {error}")
                sys.exit(1)
            else:
                print("✅ Configuration is valid")
                sys.exit(0)
        
        if args.print_config:
            config_manager.print_configuration()
            sys.exit(0)
        
        # Display configuration summary
        print(f"API Key: {'*' * (len(config.api.polygon_api_key) - 4)}{config.api.polygon_api_key[-4:]}")
        print(f"Initial Capital: ${config.strategy.initial_capital:,.2f}")
        print(f"Backtest Period: {config.strategy.start_date} to {config.strategy.end_date}")
        print(f"Underlying: {config.strategy.underlying_symbol}")
        print(f"Contract Type: {config.strategy.contract_type}")
        print(f"Entry Time: {config.strategy.entry_time}")
        print(f"Exit Time: {config.strategy.exit_time}")
        print()
        
        # Initialize strategy with configuration
        strategy = SPXOvernightStrategy(config=config)
        
        # Run backtest
        logger.info("Starting backtest...")
        results = strategy.run_backtest(
            config.strategy.start_date, 
            config.strategy.end_date
        )
        
        if results:
            # Print performance summary
            strategy.print_performance_summary()

            # Export results
            strategy.export_results(config.reporting.report_directory)

            # Generate comprehensive report with equity curve and PDF
            try:
                from src.performance_analysis import PerformanceAnalyzer

                print(f"\n📊 Generating comprehensive performance report...")
                analyzer = PerformanceAnalyzer(config.reporting.report_directory, config=config)

                # Generate markdown report with equity curve and visualizations
                report_file = analyzer.generate_comprehensive_report(
                    output_file=os.path.join(config.reporting.report_directory, "performance_report.md")
                )
                print(f"📄 Markdown report generated: {report_file}")

                # Generate PDF report if enabled
                if config.reporting.generate_pdf:
                    try:
                        pdf_file = analyzer.generate_pdf_report(
                            output_file=os.path.join(config.reporting.report_directory, "performance_report.pdf")
                        )
                        print(f"📑 PDF report generated: {pdf_file}")
                    except ImportError:
                        print("⚠️  PDF generation skipped (fpdf2 not available)")
                        logger.warning("PDF generation skipped - fpdf2 not available")
                    except Exception as e:
                        print(f"⚠️  PDF generation failed: {e}")
                        logger.warning(f"PDF generation failed: {e}")

                # List generated files
                print(f"\n📁 Generated files:")
                expected_files = [
                    "performance_report.md",
                    "performance_summary.json",
                    "trade_history.csv",
                    "daily_performance.csv",
                    "equity_curve.png",
                    "pnl_distribution.png",
                    "trade_analysis.png"
                ]

                if config.reporting.generate_pdf:
                    expected_files.append("performance_report.pdf")

                for file in expected_files:
                    file_path = os.path.join(config.reporting.report_directory, file)
                    if os.path.exists(file_path):
                        print(f"  ✅ {file}")
                    else:
                        print(f"  ❌ {file} (not found)")

            except Exception as e:
                print(f"⚠️  Report generation failed: {e}")
                logger.warning(f"Report generation failed: {e}")

            print(f"\n✅ Backtest completed successfully!")
            print(f"📊 Check the '{config.reporting.report_directory}' directory for detailed results.")

            # Log final statistics
            summary = strategy.get_performance_summary()
            logger.info(f"Backtest completed - Total Return: {summary.get('total_return_pct', 0):.2f}%")
            
        else:
            print("❌ Backtest failed to complete")
            logger.error("Backtest failed to complete")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  Backtest interrupted by user")
        logger.info("Backtest interrupted by user")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Error running backtest: {e}")
        logger.error(f"Error running backtest: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
