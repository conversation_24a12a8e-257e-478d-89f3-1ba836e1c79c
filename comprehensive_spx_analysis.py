#!/usr/bin/env python3
"""
Comprehensive SPX Options Strategy Analysis
Tests multiple timing combinations and analyzes Greeks using ONLY real Polygon data
"""

import os
import sys
import argparse
import logging
from datetime import datetime, timedelta
import pandas as pd

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import StrategyConfiguration as Config
from src.multi_timing_strategy import MultiTimingStrategy
from src.greeks_calculator import GreeksCalculator
from src.data_retrieval import PolygonDataRetriever


def setup_logging():
    """Setup comprehensive logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('comprehensive_analysis.log'),
            logging.StreamHandler()
        ]
    )


def validate_polygon_data_availability(config: Config, start_date: str, end_date: str):
    """
    Validate that real Polygon data is available for the analysis period
    
    Args:
        config: Configuration object
        start_date: Start date (YYYY-MM-DD)
        end_date: End date (YYYY-MM-DD)
    """
    logger = logging.getLogger(__name__)
    data_retriever = PolygonDataRetriever(config)
    
    logger.info("🔍 Validating Polygon data availability...")
    
    # Test SPX underlying data
    test_date = start_date
    spx_price = data_retriever.get_underlying_price("I:SPX", test_date)
    if spx_price:
        logger.info(f"✅ SPX underlying data available: ${spx_price:.2f} on {test_date}")
    else:
        logger.error(f"❌ SPX underlying data NOT available for {test_date}")
        return False
    
    # Test SPX options contracts
    options_chain = data_retriever.get_options_chain("SPX", None, "call")
    if options_chain:
        logger.info(f"✅ SPX options contracts available: {len(options_chain)} contracts found")
        
        # Test historical options pricing for a sample contract
        sample_option = options_chain[0]
        option_data = data_retriever.get_historical_option_price(sample_option.ticker, test_date)
        if option_data and option_data.get('close'):
            logger.info(f"✅ Historical options pricing available: {sample_option.ticker} = ${option_data['close']:.2f}")
        else:
            logger.warning(f"⚠️  Historical options pricing may be limited for {test_date}")
    else:
        logger.error(f"❌ SPX options contracts NOT available")
        return False
    
    # Test VIX data
    vix_value = data_retriever.get_vix_data(test_date)
    if vix_value:
        logger.info(f"✅ VIX data available: {vix_value:.2f} on {test_date}")
    else:
        logger.warning(f"⚠️  VIX data may be limited for {test_date}")
    
    logger.info("✅ Data validation completed - proceeding with analysis")
    return True


def run_greeks_analysis(config: Config, start_date: str, end_date: str):
    """
    Run comprehensive Greeks analysis using real Polygon data
    
    Args:
        config: Configuration object
        start_date: Start date for analysis
        end_date: End date for analysis
    """
    logger = logging.getLogger(__name__)
    logger.info("📊 Starting Greeks analysis with real Polygon data...")
    
    try:
        greeks_calc = GreeksCalculator(config)
        data_retriever = PolygonDataRetriever(config)
        
        # Get available SPX options for analysis
        options_chain = data_retriever.get_options_chain("SPX", None, "call")
        if not options_chain:
            logger.error("❌ No SPX options available for Greeks analysis")
            return None
        
        # Filter for OTM options (50-75 points above current SPX)
        spx_price = data_retriever.get_underlying_price("I:SPX", start_date)
        if not spx_price:
            logger.error("❌ Could not get SPX price for Greeks analysis")
            return None
        
        # Find suitable options for analysis
        suitable_options = []
        for option in options_chain:
            if (option.strike_price > spx_price + 50 and 
                option.strike_price < spx_price + 75):
                suitable_options.append(option)
        
        if not suitable_options:
            logger.warning("⚠️  No suitable OTM options found, using available options")
            suitable_options = options_chain[:5]  # Use first 5 available
        
        logger.info(f"📈 Analyzing {len(suitable_options)} options for Greeks patterns")
        
        greeks_results = []
        
        for option in suitable_options[:3]:  # Analyze top 3 to avoid rate limits
            logger.info(f"🔍 Analyzing Greeks for {option.ticker}")
            
            # Get expiration date (use a reasonable future date)
            expiration_date = data_retriever.get_next_expiration_date(start_date, 7, 30)
            if not expiration_date:
                expiration_date = (datetime.strptime(start_date, '%Y-%m-%d') + timedelta(days=14)).strftime('%Y-%m-%d')
            
            # Analyze overnight Greeks changes
            greeks_df = greeks_calc.analyze_overnight_greeks_changes(
                option.ticker, "I:SPX", option.strike_price, 
                expiration_date, start_date, end_date
            )
            
            if not greeks_df.empty:
                greeks_results.append({
                    'option_ticker': option.ticker,
                    'strike_price': option.strike_price,
                    'greeks_data': greeks_df
                })
                logger.info(f"✅ Greeks analysis completed for {option.ticker}: {len(greeks_df)} days of data")
            else:
                logger.warning(f"⚠️  No Greeks data available for {option.ticker}")
        
        # Generate Greeks analysis report
        if greeks_results:
            greeks_report_path = generate_greeks_report(greeks_results, start_date, end_date)
            logger.info(f"📊 Greeks analysis report generated: {greeks_report_path}")
            return greeks_results
        else:
            logger.warning("⚠️  No Greeks analysis results available")
            return None
            
    except Exception as e:
        logger.error(f"❌ Error in Greeks analysis: {e}")
        return None


def generate_greeks_report(greeks_results, start_date, end_date):
    """Generate comprehensive Greeks analysis report"""
    try:
        report_dir = "report/greeks_analysis"
        os.makedirs(report_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = os.path.join(report_dir, f"greeks_analysis_{timestamp}.md")
        
        with open(report_path, 'w') as f:
            f.write(f"# SPX Options Greeks Analysis Report\n\n")
            f.write(f"**Analysis Period**: {start_date} to {end_date}\n")
            f.write(f"**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**Data Source**: Real Polygon.io market data\n\n")
            
            f.write(f"## Summary\n\n")
            f.write(f"- **Options Analyzed**: {len(greeks_results)}\n")
            f.write(f"- **Data Source**: 100% Real Polygon market data\n")
            f.write(f"- **Analysis Type**: Overnight Greeks changes\n\n")
            
            for i, result in enumerate(greeks_results, 1):
                option_ticker = result['option_ticker']
                strike_price = result['strike_price']
                greeks_df = result['greeks_data']
                
                f.write(f"## Option {i}: {option_ticker}\n\n")
                f.write(f"- **Strike Price**: ${strike_price:.2f}\n")
                f.write(f"- **Data Points**: {len(greeks_df)} days\n\n")
                
                if not greeks_df.empty:
                    # Calculate summary statistics
                    avg_delta = greeks_df['delta'].mean()
                    avg_gamma = greeks_df['gamma'].mean()
                    avg_theta = greeks_df['theta'].mean()
                    avg_vega = greeks_df['vega'].mean()
                    
                    f.write(f"### Average Greeks\n\n")
                    f.write(f"- **Delta**: {avg_delta:.3f}\n")
                    f.write(f"- **Gamma**: {avg_gamma:.3f}\n")
                    f.write(f"- **Theta**: {avg_theta:.3f}\n")
                    f.write(f"- **Vega**: {avg_vega:.3f}\n\n")
                    
                    # Overnight changes analysis
                    if 'delta_change' in greeks_df.columns:
                        avg_delta_change = greeks_df['delta_change'].mean()
                        avg_theta_change = greeks_df['theta_change'].mean()
                        
                        f.write(f"### Overnight Changes\n\n")
                        f.write(f"- **Average Delta Change**: {avg_delta_change:.4f}\n")
                        f.write(f"- **Average Theta Change**: {avg_theta_change:.4f}\n\n")
        
        return report_path
        
    except Exception as e:
        logging.getLogger(__name__).error(f"Error generating Greeks report: {e}")
        return ""


def main():
    """Main function for comprehensive SPX analysis"""
    parser = argparse.ArgumentParser(description='Comprehensive SPX Options Strategy Analysis')
    parser.add_argument('--start-date', required=True, help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', required=True, help='End date (YYYY-MM-DD)')
    parser.add_argument('--initial-capital', type=float, default=100000, help='Initial capital')
    parser.add_argument('--skip-timing', action='store_true', help='Skip multi-timing analysis')
    parser.add_argument('--skip-greeks', action='store_true', help='Skip Greeks analysis')
    parser.add_argument('--validate-only', action='store_true', help='Only validate data availability')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Starting Comprehensive SPX Options Strategy Analysis")
    logger.info("=" * 80)
    logger.info(f"📅 Analysis Period: {args.start_date} to {args.end_date}")
    logger.info(f"💰 Initial Capital: ${args.initial_capital:,.2f}")
    logger.info(f"📊 Data Source: Real Polygon.io market data ONLY")
    logger.info("=" * 80)
    
    try:
        # Load configuration
        config = Config()
        
        # Validate data availability
        if not validate_polygon_data_availability(config, args.start_date, args.end_date):
            logger.error("❌ Data validation failed - cannot proceed with analysis")
            return 1
        
        if args.validate_only:
            logger.info("✅ Data validation completed successfully")
            return 0
        
        # Run Greeks analysis
        if not args.skip_greeks:
            logger.info("\n" + "="*60)
            logger.info("📊 STARTING GREEKS ANALYSIS")
            logger.info("="*60)
            
            greeks_results = run_greeks_analysis(config, args.start_date, args.end_date)
            
            if greeks_results:
                logger.info("✅ Greeks analysis completed successfully")
            else:
                logger.warning("⚠️  Greeks analysis completed with limited results")
        
        # Run multi-timing analysis
        if not args.skip_timing:
            logger.info("\n" + "="*60)
            logger.info("⏰ STARTING MULTI-TIMING ANALYSIS")
            logger.info("="*60)
            
            multi_timing = MultiTimingStrategy(config)
            timing_results = multi_timing.run_comprehensive_analysis(
                args.start_date, args.end_date, args.initial_capital
            )
            
            if timing_results:
                logger.info("✅ Multi-timing analysis completed successfully")
            else:
                logger.warning("⚠️  Multi-timing analysis completed with limited results")
        
        logger.info("\n" + "="*80)
        logger.info("🎉 COMPREHENSIVE ANALYSIS COMPLETED SUCCESSFULLY")
        logger.info("📁 Check the 'report' directory for detailed results")
        logger.info("="*80)
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Critical error in comprehensive analysis: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
