#!/usr/bin/env python3
"""
Test historical SPX options data availability for June 2024
"""

import os
import requests
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv()

API_KEY = os.getenv('POLYGON_API_KEY')
BASE_URL = "https://api.polygon.io"

def test_spx_options_for_date_range(start_date='2024-06-01', end_date='2024-06-30'):
    """Test what SPX options contracts were available during June 2024"""
    print(f"=== Testing SPX Options Available from {start_date} to {end_date} ===")
    
    # Test different contract types and expiration patterns
    test_cases = [
        # Standard monthly expirations around June 2024
        {'underlying': 'SPX', 'expiration': '2024-06-21'},  # June monthly
        {'underlying': 'SPX', 'expiration': '2024-07-19'},  # July monthly
        {'underlying': 'SPX', 'expiration': '2024-08-16'},  # August monthly
        
        # Weekly expirations in June 2024
        {'underlying': 'SPX', 'expiration': '2024-06-07'},  # First Friday
        {'underlying': 'SPX', 'expiration': '2024-06-14'},  # Second Friday
        {'underlying': 'SPX', 'expiration': '2024-06-28'},  # Fourth Friday
        
        # Try SPXW (weekly SPX options)
        {'underlying': 'SPXW', 'expiration': '2024-06-07'},
        {'underlying': 'SPXW', 'expiration': '2024-06-14'},
        {'underlying': 'SPXW', 'expiration': '2024-06-21'},
        {'underlying': 'SPXW', 'expiration': '2024-06-28'},
    ]
    
    found_contracts = []
    
    for case in test_cases:
        print(f"\nTesting {case['underlying']} expiring {case['expiration']}")
        
        endpoint = f"{BASE_URL}/v3/reference/options/contracts"
        params = {
            'underlying_ticker': case['underlying'],
            'expiration_date': case['expiration'],
            'contract_type': 'call',
            'limit': 50,
            'apikey': API_KEY
        }
        
        try:
            response = requests.get(endpoint, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('results'):
                    contracts = data['results']
                    print(f"✅ Found {len(contracts)} contracts")
                    
                    # Show sample contracts with strikes near SPX level (~5280)
                    relevant_contracts = []
                    for contract in contracts:
                        strike = contract.get('strike_price', 0)
                        if 5000 <= strike <= 5500:  # Near SPX level
                            relevant_contracts.append(contract)
                    
                    if relevant_contracts:
                        print(f"  Relevant strikes (5000-5500): {len(relevant_contracts)} contracts")
                        for contract in relevant_contracts[:5]:
                            ticker = contract.get('ticker')
                            strike = contract.get('strike_price')
                            print(f"    {ticker}: Strike ${strike}")
                            found_contracts.append(contract)
                    else:
                        print(f"  No strikes in 5000-5500 range")
                        # Show what strikes are available
                        strikes = [c.get('strike_price') for c in contracts[:10]]
                        print(f"  Available strikes: {strikes}")
                else:
                    print(f"❌ No contracts found")
            else:
                print(f"❌ API Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    return found_contracts

def test_historical_pricing_for_contracts(contracts, test_date='2024-06-03'):
    """Test historical pricing for found contracts"""
    print(f"\n=== Testing Historical Pricing for {test_date} ===")
    
    if not contracts:
        print("No contracts to test")
        return
    
    successful_pricing = []
    
    for contract in contracts[:5]:  # Test first 5 contracts
        ticker = contract.get('ticker')
        strike = contract.get('strike_price')
        
        print(f"\nTesting {ticker} (Strike: ${strike})")
        
        endpoint = f"{BASE_URL}/v2/aggs/ticker/{ticker}/range/1/day/{test_date}/{test_date}"
        params = {'apikey': API_KEY}
        
        try:
            response = requests.get(endpoint, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('results'):
                    result = data['results'][0]
                    price = result.get('c')
                    volume = result.get('v', 0)
                    print(f"✅ FOUND PRICING: Close=${price}, Volume={volume}")
                    successful_pricing.append({
                        'ticker': ticker,
                        'strike': strike,
                        'price': price,
                        'volume': volume,
                        'date': test_date
                    })
                else:
                    print(f"❌ No pricing data (queryCount: {data.get('queryCount', 0)})")
            else:
                print(f"❌ API Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    return successful_pricing

def test_different_date_ranges():
    """Test options availability across different time periods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE SPX OPTIONS HISTORICAL DATA TEST")
    print("="*60)
    
    # Test 1: June 2024 (our target period)
    print("\n1. Testing June 2024 SPX Options")
    june_contracts = test_spx_options_for_date_range('2024-06-01', '2024-06-30')
    
    if june_contracts:
        print(f"\n✅ Found {len(june_contracts)} relevant contracts for June 2024")
        pricing_results = test_historical_pricing_for_contracts(june_contracts, '2024-06-03')
        
        if pricing_results:
            print(f"\n🎉 SUCCESS! Found {len(pricing_results)} contracts with historical pricing")
            for result in pricing_results:
                print(f"  {result['ticker']}: ${result['price']} on {result['date']}")
        else:
            print(f"\n❌ No historical pricing found for June 2024 contracts")
    else:
        print(f"\n❌ No relevant SPX contracts found for June 2024")
    
    # Test 2: Try more recent dates
    print(f"\n2. Testing Recent SPX Options (2024-12)")
    recent_contracts = []
    
    # Try December 2024 expiration
    endpoint = f"{BASE_URL}/v3/reference/options/contracts"
    params = {
        'underlying_ticker': 'SPX',
        'expiration_date': '2024-12-20',
        'contract_type': 'call',
        'limit': 50,
        'apikey': API_KEY
    }
    
    try:
        response = requests.get(endpoint, params=params)
        if response.status_code == 200:
            data = response.json()
            if data.get('results'):
                contracts = data['results']
                print(f"✅ Found {len(contracts)} contracts for Dec 2024")
                
                # Test pricing for a recent date
                if contracts:
                    test_contract = contracts[0]
                    ticker = test_contract.get('ticker')
                    print(f"Testing recent pricing for {ticker}")
                    
                    # Try November 2024 pricing
                    pricing_endpoint = f"{BASE_URL}/v2/aggs/ticker/{ticker}/range/1/day/2024-11-01/2024-11-01"
                    pricing_response = requests.get(pricing_endpoint, params={'apikey': API_KEY})
                    
                    if pricing_response.status_code == 200:
                        pricing_data = pricing_response.json()
                        if pricing_data.get('results'):
                            print(f"✅ Found recent pricing data!")
                        else:
                            print(f"❌ No recent pricing data")
                    
    except Exception as e:
        print(f"❌ Exception testing recent contracts: {e}")
    
    return june_contracts, pricing_results if 'pricing_results' in locals() else []

if __name__ == "__main__":
    contracts, pricing = test_different_date_ranges()
    
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    if pricing:
        print(f"✅ SUCCESS: Found {len(pricing)} SPX options with historical pricing")
        print("✅ Your Polygon subscription DOES support historical options data")
        print("✅ Ready to implement full SPX options backtesting")
    else:
        print("❌ No historical SPX options pricing found")
        print("❓ This could be due to:")
        print("   1. SPX options contracts not existing for those specific dates")
        print("   2. Different ticker format needed (SPXW, SPY, etc.)")
        print("   3. Need to use different date ranges")
        
    print(f"\nAPI Status: Working correctly")
    print(f"Subscription: Active (not free tier)")
    print(f"Stock Data: ✅ Available")
    print(f"Options Contracts: ✅ Available")
    print(f"Historical Options Pricing: {'✅ Available' if pricing else '❌ Limited'}")
