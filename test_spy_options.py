#!/usr/bin/env python3
"""
Test SPY options data availability (SPY is much more liquid than SPX)
"""

import os
import requests
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv()

API_KEY = os.getenv('POLYGON_API_KEY')
BASE_URL = "https://api.polygon.io"

def test_spy_options_for_june_2024():
    """Test SPY options for June 2024"""
    print("=== Testing SPY Options for June 2024 ===")
    
    # SPY typically trades around $520-530 in June 2024
    # Test different expiration dates
    test_expirations = [
        '2024-06-07',   # Weekly
        '2024-06-14',   # Weekly  
        '2024-06-21',   # Monthly
        '2024-06-28',   # Weekly
        '2024-07-19',   # Next monthly
    ]
    
    found_contracts = []
    
    for exp_date in test_expirations:
        print(f"\nTesting SPY options expiring {exp_date}")
        
        endpoint = f"{BASE_URL}/v3/reference/options/contracts"
        params = {
            'underlying_ticker': 'SPY',
            'expiration_date': exp_date,
            'contract_type': 'call',
            'limit': 100,
            'apikey': API_KEY
        }
        
        try:
            response = requests.get(endpoint, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('results'):
                    contracts = data['results']
                    print(f"✅ Found {len(contracts)} SPY call contracts")
                    
                    # Look for strikes around SPY price (~$520-530)
                    relevant_contracts = []
                    for contract in contracts:
                        strike = contract.get('strike_price', 0)
                        if 500 <= strike <= 550:  # Near SPY level
                            relevant_contracts.append(contract)
                    
                    print(f"  Contracts with strikes 500-550: {len(relevant_contracts)}")
                    
                    if relevant_contracts:
                        for contract in relevant_contracts[:5]:
                            ticker = contract.get('ticker')
                            strike = contract.get('strike_price')
                            print(f"    {ticker}: Strike ${strike}")
                        
                        found_contracts.extend(relevant_contracts[:10])  # Keep top 10
                        
                        # Test historical pricing immediately
                        print(f"  Testing historical pricing for {relevant_contracts[0].get('ticker')}")
                        test_ticker = relevant_contracts[0].get('ticker')
                        
                        pricing_endpoint = f"{BASE_URL}/v2/aggs/ticker/{test_ticker}/range/1/day/2024-06-03/2024-06-03"
                        pricing_response = requests.get(pricing_endpoint, params={'apikey': API_KEY})
                        
                        if pricing_response.status_code == 200:
                            pricing_data = pricing_response.json()
                            if pricing_data.get('results'):
                                result = pricing_data['results'][0]
                                price = result.get('c')
                                volume = result.get('v', 0)
                                print(f"    🎉 FOUND PRICING: ${price}, Volume: {volume}")
                                return found_contracts  # Success!
                            else:
                                print(f"    ❌ No pricing data")
                        else:
                            print(f"    ❌ Pricing API error: {pricing_response.status_code}")
                else:
                    print(f"❌ No contracts found")
            else:
                print(f"❌ API Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    return found_contracts

def test_spy_comprehensive():
    """Comprehensive SPY options test"""
    print("="*60)
    print("COMPREHENSIVE SPY OPTIONS TEST")
    print("="*60)
    
    # First, get SPY price for June 3, 2024
    print("\n1. Testing SPY underlying price")
    spy_endpoint = f"{BASE_URL}/v2/aggs/ticker/SPY/range/1/day/2024-06-03/2024-06-03"
    spy_response = requests.get(spy_endpoint, params={'apikey': API_KEY})
    
    spy_price = None
    if spy_response.status_code == 200:
        spy_data = spy_response.json()
        if spy_data.get('results'):
            spy_price = spy_data['results'][0]['c']
            print(f"✅ SPY price on 2024-06-03: ${spy_price}")
        else:
            print("❌ No SPY price data")
    else:
        print(f"❌ SPY price API error: {spy_response.status_code}")
    
    # Test SPY options
    print("\n2. Testing SPY options contracts and pricing")
    contracts = test_spy_options_for_june_2024()
    
    if contracts:
        print(f"\n✅ SUCCESS! Found {len(contracts)} SPY options contracts")
        
        # Test multiple contracts for pricing
        print("\n3. Testing pricing for multiple contracts")
        successful_pricing = []
        
        for contract in contracts[:5]:
            ticker = contract.get('ticker')
            strike = contract.get('strike_price')
            
            print(f"\nTesting {ticker} (Strike: ${strike})")
            
            # Test multiple dates
            test_dates = ['2024-06-03', '2024-06-04', '2024-06-05']
            for test_date in test_dates:
                pricing_endpoint = f"{BASE_URL}/v2/aggs/ticker/{ticker}/range/1/day/{test_date}/{test_date}"
                pricing_response = requests.get(pricing_endpoint, params={'apikey': API_KEY})
                
                if pricing_response.status_code == 200:
                    pricing_data = pricing_response.json()
                    if pricing_data.get('results'):
                        result = pricing_data['results'][0]
                        price = result.get('c')
                        volume = result.get('v', 0)
                        print(f"  ✅ {test_date}: ${price}, Volume: {volume}")
                        successful_pricing.append({
                            'ticker': ticker,
                            'strike': strike,
                            'date': test_date,
                            'price': price,
                            'volume': volume
                        })
                        break  # Found pricing, move to next contract
                    else:
                        print(f"  ❌ {test_date}: No data")
                else:
                    print(f"  ❌ {test_date}: API error")
        
        return contracts, successful_pricing, spy_price
    else:
        print("\n❌ No SPY options contracts found")
        return [], [], spy_price

if __name__ == "__main__":
    contracts, pricing, spy_price = test_spy_comprehensive()
    
    print("\n" + "="*60)
    print("FINAL RESULTS")
    print("="*60)
    
    if pricing:
        print(f"🎉 EXCELLENT! Found historical SPY options data:")
        print(f"   📊 SPY Price (2024-06-03): ${spy_price}")
        print(f"   📋 Options Contracts: {len(contracts)}")
        print(f"   💰 Historical Pricing Records: {len(pricing)}")
        print(f"\n📈 Sample pricing data:")
        for p in pricing[:3]:
            print(f"   {p['ticker']}: ${p['price']} on {p['date']} (Volume: {p['volume']})")
        
        print(f"\n✅ RECOMMENDATION: Use SPY options instead of SPX")
        print(f"   - SPY options have excellent historical data availability")
        print(f"   - SPY tracks SPX very closely (correlation ~0.99)")
        print(f"   - Much more liquid and widely traded")
        print(f"   - Perfect for overnight options strategies")
        
    else:
        print("❌ No historical options pricing found")
        
    print(f"\n🔧 Technical Status:")
    print(f"   API Access: ✅ Working")
    print(f"   Subscription: ✅ Active (not free tier)")
    print(f"   Stock Data: ✅ Available")
    print(f"   SPY Options: {'✅ Available' if pricing else '❌ Limited'}")
    print(f"   SPX Options: ❌ Not available for historical dates")
