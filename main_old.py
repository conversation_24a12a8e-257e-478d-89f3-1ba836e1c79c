"""
Main script to run SPX Overnight Options Strategy Backtest
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from overnight_strategy import SPXOvernightStrategy

def main():
    """
    Main function to run the backtest
    """
    print("SPX Overnight Options Strategy Backtest")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Get parameters from environment
    api_key = os.getenv('POLYGON_API_KEY')
    initial_capital = float(os.getenv('INITIAL_CAPITAL', 100000))
    start_date = os.getenv('START_DATE', '2023-01-01')
    end_date = os.getenv('END_DATE', '2024-12-31')
    
    if not api_key:
        print("Error: POLYGON_API_KEY not found in environment variables")
        return
    
    print(f"API Key: {'*' * (len(api_key) - 4)}{api_key[-4:]}")
    print(f"Initial Capital: ${initial_capital:,.2f}")
    print(f"Backtest Period: {start_date} to {end_date}")
    print()
    
    try:
        # Initialize strategy
        strategy = SPXOvernightStrategy(
            api_key=api_key,
            initial_capital=initial_capital
        )
        
        # Run backtest
        results = strategy.run_backtest(start_date, end_date)
        
        if results:
            # Print performance summary
            strategy.print_performance_summary()
            
            # Export results
            strategy.export_results("report")
            
            print("\nBacktest completed successfully!")
            print("Check the 'report' directory for detailed results.")
        else:
            print("Backtest failed to complete")
            
    except Exception as e:
        print(f"Error running backtest: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

