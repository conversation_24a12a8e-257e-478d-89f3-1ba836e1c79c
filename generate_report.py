"""
Refactored Report Generator for SPX Overnight Options Strategy
Uses the new configuration system and enhanced reporting capabilities
"""

import os
import sys
import argparse
from datetime import datetime
import logging

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from config_manager import ConfigurationManager, load_config
from performance_analysis import PerformanceAnalyzer


def setup_logging(log_level: str = 'INFO') -> None:
    """
    Set up logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
    """
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('report_generation.log')
        ]
    )


def parse_arguments() -> argparse.Namespace:
    """
    Parse command line arguments
    
    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(
        description='SPX Overnight Options Strategy Report Generator',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python generate_report.py                    # Generate with default settings
  python generate_report.py --config custom.json  # Use custom configuration
  python generate_report.py --no-pdf           # Skip PDF generation
  python generate_report.py --verbose          # Enable debug logging
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='Path to configuration file (JSON format)'
    )
    
    parser.add_argument(
        '--report-dir',
        type=str,
        help='Directory containing trade history and results'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        help='Output directory for generated reports'
    )
    
    parser.add_argument(
        '--no-pdf',
        action='store_true',
        help='Skip PDF report generation'
    )
    
    parser.add_argument(
        '--no-plots',
        action='store_true',
        help='Skip plot generation'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose (debug) logging'
    )
    
    return parser.parse_args()


def main():
    """
    Generate comprehensive performance analysis and report
    """
    # Parse command line arguments
    args = parse_arguments()
    
    # Set up logging
    log_level = 'DEBUG' if args.verbose else 'INFO'
    setup_logging(log_level)
    
    logger = logging.getLogger(__name__)
    
    try:
        print("SPX Overnight Options Strategy - Report Generator")
        print("=" * 60)
        
        # Load configuration
        config_manager = ConfigurationManager(args.config)
        config = config_manager.load_configuration()
        
        # Override configuration with CLI arguments
        report_dir = args.report_dir or config.reporting.report_directory
        output_dir = args.output_dir or config.reporting.report_directory
        
        # Update configuration
        if args.no_pdf:
            config.reporting.generate_pdf = False
        if args.no_plots:
            config.reporting.generate_plots = False
        
        print(f"Report Directory: {report_dir}")
        print(f"Output Directory: {output_dir}")
        print(f"Generate PDF: {config.reporting.generate_pdf}")
        print(f"Generate Plots: {config.reporting.generate_plots}")
        print()
        
        # Check if trade history exists
        trade_history_file = os.path.join(report_dir, "trade_history.csv")
        if not os.path.exists(trade_history_file):
            print(f"❌ Error: Trade history file not found: {trade_history_file}")
            print("Please run the backtest first using main.py")
            logger.error(f"Trade history file not found: {trade_history_file}")
            sys.exit(1)
        
        # Initialize performance analyzer with configuration
        analyzer = PerformanceAnalyzer(report_dir, config=config)
        
        logger.info("Starting report generation...")
        
        # Generate comprehensive report
        report_file = analyzer.generate_comprehensive_report(
            output_file=os.path.join(output_dir, "performance_report.md")
        )
        print(f"📄 Markdown report generated: {report_file}")
        
        # Generate PDF report if enabled
        if config.reporting.generate_pdf:
            try:
                pdf_file = analyzer.generate_pdf_report(
                    output_file=os.path.join(output_dir, "performance_report.pdf")
                )
                print(f"📑 PDF report generated: {pdf_file}")
            except ImportError:
                print("⚠️  PDF generation skipped (fpdf2 not available)")
                logger.warning("PDF generation skipped - fpdf2 not available")
            except Exception as e:
                print(f"❌ PDF generation failed: {e}")
                logger.error(f"PDF generation failed: {e}")
        
        # Generate additional analysis files
        try:
            # Export detailed metrics
            metrics_file = os.path.join(output_dir, "detailed_metrics.json")
            analyzer.export_detailed_metrics(metrics_file)
            print(f"📊 Detailed metrics exported: {metrics_file}")
            
            # Generate summary statistics
            summary_file = os.path.join(output_dir, "performance_summary.json")
            analyzer.export_performance_summary(summary_file)
            print(f"📈 Performance summary exported: {summary_file}")
            
        except Exception as e:
            print(f"⚠️  Warning: Additional analysis generation failed: {e}")
            logger.warning(f"Additional analysis generation failed: {e}")
        
        # List all generated files
        print(f"\n📁 Generated files:")
        expected_files = [
            "performance_report.md",
            "performance_summary.json",
            "trade_history.csv",
            "daily_performance.csv"
        ]
        
        if config.reporting.generate_plots:
            expected_files.extend([
                "equity_curve.png",
                "pnl_distribution.png", 
                "trade_analysis.png"
            ])
        
        if config.reporting.generate_pdf:
            expected_files.append("performance_report.pdf")
        
        for file in expected_files:
            file_path = os.path.join(output_dir, file)
            if os.path.exists(file_path):
                print(f"  ✅ {file}")
            else:
                print(f"  ❌ {file} (not found)")
        
        print(f"\n✅ Report generation completed successfully!")
        print(f"📁 Check the '{output_dir}' directory for all generated files.")
        
        logger.info("Report generation completed successfully")
        
    except KeyboardInterrupt:
        print("\n⚠️  Report generation interrupted by user")
        logger.info("Report generation interrupted by user")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Error generating report: {e}")
        logger.error(f"Error generating report: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
