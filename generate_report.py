"""
Generate comprehensive performance report for SPX Overnight Options Strategy
"""

import os
import sys
from datetime import datetime

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from performance_analysis import PerformanceAnalyzer

def main():
    """
    Generate comprehensive performance analysis and report
    """
    print("Generating SPX Overnight Options Strategy Performance Report")
    print("=" * 60)
    
    try:
        # Initialize analyzer
        analyzer = PerformanceAnalyzer(report_dir="report")
        
        # Generate comprehensive report
        report_path = analyzer.generate_comprehensive_report()
        
        print(f"\nReport generated successfully!")
        print(f"Report location: {report_path}")
        
        # List all generated files
        print(f"\nGenerated files:")
        report_files = [
            "performance_report.md",
            "equity_curve.png", 
            "pnl_distribution.png",
            "trade_analysis.png",
            "trade_history.csv",
            "daily_performance.csv",
            "performance_summary.json"
        ]
        
        for file in report_files:
            file_path = os.path.join("report", file)
            if os.path.exists(file_path):
                print(f"  ✓ {file}")
            else:
                print(f"  ✗ {file} (not found)")
        
        print(f"\nAll files are available in the 'report' directory")
        
    except Exception as e:
        print(f"Error generating report: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

