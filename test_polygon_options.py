#!/usr/bin/env python3
"""
Test script to check Polygon options data availability
"""

import os
import requests
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv()

API_KEY = os.getenv('POLYGON_API_KEY')
BASE_URL = "https://api.polygon.io"

def test_options_contracts(underlying='SPX', expiration_date='2024-06-21'):
    """Test options contracts endpoint"""
    print(f"\n=== Testing Options Contracts for {underlying} expiring {expiration_date} ===")

    endpoint = f"{BASE_URL}/v3/reference/options/contracts"
    params = {
        'underlying_ticker': underlying,
        'contract_type': 'call',
        'limit': 10,
        'apikey': API_KEY
    }

    # Only add expiration_date if provided
    if expiration_date:
        params['expiration_date'] = expiration_date
    
    try:
        response = requests.get(endpoint, params=params)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response Status: {data.get('status')}")
            
            if data.get('results'):
                print(f"Found {len(data['results'])} contracts")
                for i, contract in enumerate(data['results'][:3]):  # Show first 3
                    print(f"  {i+1}. {contract.get('ticker')} - Strike: ${contract.get('strike_price')}")
                return data['results']
            else:
                print("No contracts found")
                return []
        else:
            print(f"Error: {response.text}")
            return []
            
    except Exception as e:
        print(f"Exception: {e}")
        return []

def test_option_pricing(option_ticker, date='2024-06-03'):
    """Test historical option pricing"""
    print(f"\n=== Testing Historical Pricing for {option_ticker} on {date} ===")

    endpoint = f"{BASE_URL}/v2/aggs/ticker/{option_ticker}/range/1/day/{date}/{date}"
    params = {'apikey': API_KEY}

    try:
        response = requests.get(endpoint, params=params)
        print(f"Status Code: {response.status_code}")
        print(f"Full URL: {endpoint}?apikey=***")

        if response.status_code == 200:
            data = response.json()
            print(f"Response Status: {data.get('status')}")
            print(f"Full Response: {data}")

            if data.get('results'):
                result = data['results'][0]
                print(f"Price Data: Open=${result.get('o')}, High=${result.get('h')}, Low=${result.get('l')}, Close=${result.get('c')}")
                print(f"Volume: {result.get('v')}")
                return result.get('c')  # closing price
            else:
                print("No pricing data found")
                print(f"Response details: {data}")
                return None
        else:
            print(f"Error Response: {response.text}")
            print(f"Headers: {response.headers}")
            return None

    except Exception as e:
        print(f"Exception: {e}")
        return None

def test_current_spx_price(date='2024-06-03'):
    """Test SPX price retrieval"""
    print(f"\n=== Testing SPX Price for {date} ===")
    
    endpoint = f"{BASE_URL}/v2/aggs/ticker/I:SPX/range/1/day/{date}/{date}"
    params = {'apikey': API_KEY}
    
    try:
        response = requests.get(endpoint, params=params)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('results'):
                price = data['results'][0]['c']
                print(f"SPX Price: ${price}")
                return price
            else:
                print("No SPX data found")
                return None
        else:
            print(f"Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"Exception: {e}")
        return None

if __name__ == "__main__":
    print("Testing Polygon Options Data Availability")
    print("=" * 50)
    
    # Test SPX price
    spx_price = test_current_spx_price('2024-06-03')

    # Test options contracts for AAPL first (more likely to have data)
    print("\n=== Testing AAPL Options (for comparison) ===")
    aapl_contracts = test_options_contracts('AAPL', '2024-06-21')

    # Test SPX options contracts without expiration filter
    print("\n=== Testing SPX Options (no expiration filter) ===")
    contracts = test_options_contracts('SPX', None)

    # Try SPXW (SPX weeklies)
    print("\n=== Testing SPXW Options ===")
    spxw_contracts = test_options_contracts('SPXW', None)

    # Test with current date
    from datetime import datetime
    current_date = datetime.now().strftime('%Y-%m-%d')
    print(f"\n=== Testing with current date {current_date} ===")
    current_contracts = test_options_contracts('AAPL', None)
    
    if contracts:
        # Test pricing for first contract
        first_contract = contracts[0]
        option_ticker = first_contract.get('ticker')
        if option_ticker:
            print(f"\n=== Testing pricing for {option_ticker} ===")
            # Test multiple dates
            test_dates = ['2024-06-03', '2024-06-04', '2024-06-05', '2024-06-06', '2024-06-07']
            for test_date in test_dates:
                result = test_option_pricing(option_ticker, test_date)
                if result:
                    print(f"✅ Found pricing data for {test_date}")
                    break

    # Try different expiration dates for SPX
    print("\n=== Testing SPX Options with Different Expiration Dates ===")
    test_expirations = ['2024-06-07', '2024-06-14', '2024-06-21', '2024-06-28', '2024-07-05']
    for exp_date in test_expirations:
        print(f"\nTesting expiration: {exp_date}")
        exp_contracts = test_options_contracts('SPX', exp_date)
        if exp_contracts:
            print(f"✅ Found {len(exp_contracts)} contracts for {exp_date}")
            # Test pricing for first contract
            first_contract = exp_contracts[0]
            option_ticker = first_contract.get('ticker')
            if option_ticker:
                print(f"Testing pricing for {option_ticker}")
                result = test_option_pricing(option_ticker, '2024-06-03')
                if result:
                    print(f"✅ FOUND HISTORICAL OPTIONS PRICING!")
                    break
        else:
            print(f"❌ No contracts for {exp_date}")

    # Test subscription tier info
    print("\n=== Testing Subscription Tier ===")
    test_url = f"{BASE_URL}/v2/aggs/ticker/AAPL/range/1/day/2024-06-03/2024-06-03"
    response = requests.get(test_url, params={'apikey': API_KEY})
    print(f"AAPL test status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"AAPL response: {data}")
    else:
        print(f"AAPL error: {response.text}")

    # Check rate limits and subscription info
    print(f"Response headers: {response.headers}")
    if 'X-RateLimit-Limit' in response.headers:
        print(f"Rate limit: {response.headers['X-RateLimit-Limit']}")
    if 'X-RateLimit-Remaining' in response.headers:
        print(f"Rate limit remaining: {response.headers['X-RateLimit-Remaining']}")
