#!/usr/bin/env python3
"""
Test current options data to understand Polygon's options data model
"""

import os
import requests
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv()

API_KEY = os.getenv('POLYGON_API_KEY')
BASE_URL = "https://api.polygon.io"

def test_current_options():
    """Test current options to understand data availability"""
    print("="*60)
    print("TESTING CURRENT OPTIONS DATA MODEL")
    print("="*60)
    
    # Get current date and future expiration dates
    today = datetime.now()
    print(f"Today: {today.strftime('%Y-%m-%d')}")
    
    # Test SPY options with current/future expirations
    future_dates = []
    for weeks_ahead in [1, 2, 3, 4, 8, 12]:
        future_date = today + timedelta(weeks=weeks_ahead)
        # Adjust to Friday (options typically expire on Fridays)
        days_ahead = (4 - future_date.weekday()) % 7  # 4 = Friday
        if days_ahead == 0 and future_date.weekday() != 4:
            days_ahead = 7
        future_date += timedelta(days=days_ahead)
        future_dates.append(future_date.strftime('%Y-%m-%d'))
    
    print(f"Testing future expiration dates: {future_dates}")
    
    # Test 1: SPY options with future expirations
    print(f"\n1. Testing SPY options with future expirations")
    spy_contracts = []
    
    for exp_date in future_dates:
        print(f"\nTesting SPY expiring {exp_date}")
        
        endpoint = f"{BASE_URL}/v3/reference/options/contracts"
        params = {
            'underlying_ticker': 'SPY',
            'expiration_date': exp_date,
            'contract_type': 'call',
            'limit': 50,
            'apikey': API_KEY
        }
        
        try:
            response = requests.get(endpoint, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('results'):
                    contracts = data['results']
                    print(f"✅ Found {len(contracts)} SPY contracts")
                    
                    # Show sample contracts
                    for contract in contracts[:3]:
                        ticker = contract.get('ticker')
                        strike = contract.get('strike_price')
                        print(f"  {ticker}: Strike ${strike}")
                    
                    spy_contracts.extend(contracts[:5])
                    
                    # Test current pricing for one contract
                    if contracts:
                        test_ticker = contracts[0].get('ticker')
                        print(f"  Testing current pricing for {test_ticker}")
                        
                        # Try recent date (yesterday)
                        yesterday = (today - timedelta(days=1)).strftime('%Y-%m-%d')
                        pricing_endpoint = f"{BASE_URL}/v2/aggs/ticker/{test_ticker}/range/1/day/{yesterday}/{yesterday}"
                        pricing_response = requests.get(pricing_endpoint, params={'apikey': API_KEY})
                        
                        if pricing_response.status_code == 200:
                            pricing_data = pricing_response.json()
                            if pricing_data.get('results'):
                                result = pricing_data['results'][0]
                                price = result.get('c')
                                volume = result.get('v', 0)
                                print(f"    ✅ Recent pricing ({yesterday}): ${price}, Volume: {volume}")
                            else:
                                print(f"    ❌ No recent pricing data")
                        
                    break  # Found contracts, stop testing
                else:
                    print(f"❌ No contracts for {exp_date}")
            else:
                print(f"❌ API Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    # Test 2: Check what happens with past dates
    print(f"\n2. Testing past dates to confirm theory")
    
    past_dates = [
        '2024-06-21',  # June 2024 (past)
        '2024-12-20',  # December 2024 (past)
        '2025-01-17',  # January 2025 (recent past)
    ]
    
    for past_date in past_dates:
        print(f"\nTesting SPY expiring {past_date} (past date)")
        
        endpoint = f"{BASE_URL}/v3/reference/options/contracts"
        params = {
            'underlying_ticker': 'SPY',
            'expiration_date': past_date,
            'contract_type': 'call',
            'limit': 10,
            'apikey': API_KEY
        }
        
        try:
            response = requests.get(endpoint, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('results'):
                    print(f"✅ Found {len(data['results'])} contracts (unexpected!)")
                else:
                    print(f"❌ No contracts (expected for past dates)")
            else:
                print(f"❌ API Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    # Test 3: Check current quotes for active contracts
    if spy_contracts:
        print(f"\n3. Testing current quotes for active contracts")
        
        test_contract = spy_contracts[0]
        ticker = test_contract.get('ticker')
        
        print(f"Testing current quote for {ticker}")
        
        quotes_endpoint = f"{BASE_URL}/v3/quotes/{ticker}"
        params = {'limit': 1, 'apikey': API_KEY}
        
        try:
            response = requests.get(quotes_endpoint, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('results'):
                    quote = data['results'][0]
                    bid = quote.get('bid_price', 0)
                    ask = quote.get('ask_price', 0)
                    last = quote.get('last_price', 0)
                    print(f"✅ Current quote: Bid=${bid}, Ask=${ask}, Last=${last}")
                else:
                    print(f"❌ No current quote data")
            else:
                print(f"❌ Quotes API Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    return spy_contracts

def test_polygon_data_model():
    """Test to understand Polygon's options data model"""
    print(f"\n" + "="*60)
    print("POLYGON OPTIONS DATA MODEL ANALYSIS")
    print("="*60)
    
    # Test different approaches
    print(f"\n1. Testing options contracts without expiration filter")
    
    endpoint = f"{BASE_URL}/v3/reference/options/contracts"
    params = {
        'underlying_ticker': 'SPY',
        'contract_type': 'call',
        'limit': 100,
        'apikey': API_KEY
    }
    
    try:
        response = requests.get(endpoint, params=params)
        if response.status_code == 200:
            data = response.json()
            if data.get('results'):
                contracts = data['results']
                print(f"✅ Found {len(contracts)} total SPY call contracts")
                
                # Analyze expiration dates
                expirations = set()
                for contract in contracts:
                    exp_date = contract.get('expiration_date')
                    if exp_date:
                        expirations.add(exp_date)
                
                sorted_expirations = sorted(list(expirations))
                print(f"Available expiration dates: {len(sorted_expirations)}")
                for exp in sorted_expirations[:10]:
                    print(f"  {exp}")
                
                if len(sorted_expirations) > 10:
                    print(f"  ... and {len(sorted_expirations) - 10} more")
                
                # Check if any are in the past
                today = datetime.now().strftime('%Y-%m-%d')
                past_expirations = [exp for exp in sorted_expirations if exp < today]
                future_expirations = [exp for exp in sorted_expirations if exp >= today]
                
                print(f"\nExpiration analysis:")
                print(f"  Past expirations: {len(past_expirations)}")
                print(f"  Future expirations: {len(future_expirations)}")
                
                if past_expirations:
                    print(f"  🔍 INTERESTING: Found past expirations!")
                    for exp in past_expirations[:5]:
                        print(f"    {exp}")
                
            else:
                print(f"❌ No contracts found")
        else:
            print(f"❌ API Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    contracts = test_current_options()
    test_polygon_data_model()
    
    print(f"\n" + "="*60)
    print("CONCLUSIONS")
    print("="*60)
    
    if contracts:
        print(f"✅ Polygon DOES have options contracts data")
        print(f"✅ Current/future options contracts are available")
        print(f"✅ Current options pricing/quotes work")
        print(f"❓ Historical options contracts may be limited")
        
        print(f"\n💡 STRATEGY RECOMMENDATIONS:")
        print(f"1. Use current options contracts for forward testing")
        print(f"2. Collect real-time data to build historical database")
        print(f"3. Consider using SPY instead of SPX (more liquid)")
        print(f"4. Focus on real-time strategy validation")
        
    else:
        print(f"❌ Limited options data availability")
        
    print(f"\n🔧 Next Steps:")
    print(f"1. Modify strategy to use SPY options")
    print(f"2. Implement real-time data collection")
    print(f"3. Use current contracts for strategy testing")
    print(f"4. Build forward-testing framework")
