2025-07-13 16:04:51,835 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:04:51,836 - __main__ - ERROR - Error running backtest: SPXOvernightStrategy.__init__() got an unexpected keyword argument 'config'
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/spx_overnight_strategy/main.py", line 169, in main
    strategy = SPXOvernightStrategy(config=config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: SPXOvernightStrategy.__init__() got an unexpected keyword argument 'config'
2025-07-13 16:06:01,517 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:06:01,518 - __main__ - ERROR - Error running backtest: PolygonDataRetriever.__init__() takes from 1 to 2 positional arguments but 3 were given
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/spx_overnight_strategy/main.py", line 169, in main
    strategy = SPXOvernightStrategy(config=config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/spx_overnight_strategy/src/overnight_strategy.py", line 51, in __init__
    self.data_retriever = PolygonDataRetriever(api_key, self.config)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: PolygonDataRetriever.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-07-13 16:06:31,348 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:06:31,368 - __main__ - INFO - Starting backtest...
2025-07-13 16:06:31,369 - __main__ - ERROR - Backtest failed to complete
2025-07-13 16:07:19,771 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:07:19,792 - __main__ - INFO - Starting backtest...
2025-07-13 16:07:19,913 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:07:20,005 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 169
2025-07-13 16:07:20,138 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:07:20,270 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:07:20,400 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:07:20,400 - __main__ - ERROR - Backtest failed to complete
2025-07-13 16:08:29,895 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:08:29,916 - __main__ - INFO - Starting backtest...
2025-07-13 16:08:30,030 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:08:30,180 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:08:30,318 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:08:30,458 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:08:30,590 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:08:30,726 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:08:30,862 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 173
2025-07-13 16:08:30,999 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:08:31,134 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:08:31,137 - __main__ - ERROR - Error running backtest: 'final_capital'
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/spx_overnight_strategy/main.py", line 180, in main
    strategy.print_performance_summary()
  File "/Users/<USER>/Downloads/spx_overnight_strategy/src/overnight_strategy.py", line 432, in print_performance_summary
    print(f"Final Capital:        ${perf['final_capital']:,.2f}")
                                    ~~~~^^^^^^^^^^^^^^^^^
KeyError: 'final_capital'
2025-07-13 16:10:04,344 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:10:04,365 - __main__ - INFO - Starting backtest...
2025-07-13 16:10:04,478 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:10:04,558 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 169
2025-07-13 16:10:04,746 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 173
2025-07-13 16:10:04,877 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:10:05,011 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:10:05,144 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:10:05,283 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:10:05,415 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:10:05,549 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:10:05,550 - __main__ - ERROR - Error running backtest: 'SPXOvernightStrategy' object has no attribute 'get_performance_summary'
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/spx_overnight_strategy/main.py", line 189, in main
    summary = strategy.get_performance_summary()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SPXOvernightStrategy' object has no attribute 'get_performance_summary'. Did you mean: 'print_performance_summary'?
2025-07-13 16:12:05,018 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:12:05,099 - __main__ - INFO - Starting backtest...
2025-07-13 16:12:05,229 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:12:05,314 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:12:05,445 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:12:05,576 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:12:05,577 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:12:05,579 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:12:05,710 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:12:05,838 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:12:05,971 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:12:05,971 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:12:05,972 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:12:06,102 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:12:06,234 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:12:06,364 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:12:06,365 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:12:06,365 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:12:06,500 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:12:06,634 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:12:06,763 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:12:06,764 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:12:06,764 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:12:06,765 - position_manager - WARNING - No trade history to export
2025-07-13 16:12:06,769 - __main__ - INFO - Backtest completed - Total Return: 0.00%
2025-07-13 16:13:05,157 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:13:05,282 - __main__ - INFO - Starting backtest...
2025-07-13 16:13:05,418 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:13:05,533 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 167
2025-07-13 16:13:05,752 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 173
2025-07-13 16:13:05,964 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:05,964 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:05,973 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:13:06,113 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:13:06,251 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:13:06,384 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 98
2025-07-13 16:13:06,384 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:06,385 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:13:06,517 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:13:06,653 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:13:06,788 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:06,789 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:06,790 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:13:06,918 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:13:07,051 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:13:07,175 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:07,175 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:07,176 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:13:07,176 - position_manager - WARNING - No trade history to export
2025-07-13 16:13:07,182 - __main__ - INFO - Backtest completed - Total Return: 0.00%
2025-07-13 16:13:33,885 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:13:33,907 - __main__ - INFO - Starting backtest...
2025-07-13 16:13:34,017 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:13:34,088 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:13:34,217 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:13:34,350 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:34,351 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:34,353 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:13:34,353 - position_manager - INFO - Opened position: O:SPX240119C04750000 4 contracts @ $83.58
2025-07-13 16:13:34,486 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:13:34,618 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/O:SPX240119C04750000/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 211
2025-07-13 16:13:34,619 - position_manager - INFO - Closed position: O:SPX240119C04750000 P&L: $-22553.21
2025-07-13 16:13:34,754 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:13:34,886 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:13:35,013 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:35,013 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:35,015 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:13:35,015 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:13:35,142 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:13:35,274 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:13:35,404 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:35,405 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:35,406 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:13:35,406 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:13:35,533 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:13:35,666 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:13:35,794 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:35,794 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:35,795 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:13:35,795 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:13:35,801 - position_manager - INFO - Trade history exported to report/trade_history.csv
2025-07-13 16:13:35,802 - __main__ - INFO - Backtest completed - Total Return: -22.55%
2025-07-13 16:14:26,619 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:14:26,648 - __main__ - INFO - Starting backtest...
2025-07-13 16:14:27,173 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:27,174 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:14:27,175 - position_manager - INFO - Opened position: O:SPX240119C04750000 2 contracts @ $83.58
2025-07-13 16:14:27,439 - position_manager - INFO - Closed position: O:SPX240119C04750000 P&L: $-11276.60
2025-07-13 16:14:27,843 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:27,844 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:14:27,844 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:28,241 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:28,242 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:14:28,243 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:28,641 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:28,642 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:14:28,642 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:29,045 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:29,046 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04775000 (strike: $4775.00, price: $63.89)
2025-07-13 16:14:29,046 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:29,442 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:29,444 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $69.44)
2025-07-13 16:14:29,444 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:29,836 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:29,836 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04775000 (strike: $4775.00, price: $67.28)
2025-07-13 16:14:29,837 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:30,228 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:30,229 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04775000 (strike: $4775.00, price: $61.79)
2025-07-13 16:14:30,229 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:30,624 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:30,625 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04775000 (strike: $4775.00, price: $59.76)
2025-07-13 16:14:30,625 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:30,889 - data_retrieval - WARNING - No SPX price data available for 2024-01-15
2025-07-13 16:14:30,894 - position_manager - INFO - Trade history exported to report/trade_history.csv
2025-07-13 16:14:30,895 - __main__ - INFO - Backtest completed - Total Return: -22.55%
