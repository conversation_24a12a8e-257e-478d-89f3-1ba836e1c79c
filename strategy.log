2025-07-13 16:04:51,835 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:04:51,836 - __main__ - ERROR - Error running backtest: SPXOvernightStrategy.__init__() got an unexpected keyword argument 'config'
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/spx_overnight_strategy/main.py", line 169, in main
    strategy = SPXOvernightStrategy(config=config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: SPXOvernightStrategy.__init__() got an unexpected keyword argument 'config'
2025-07-13 16:06:01,517 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:06:01,518 - __main__ - ERROR - Error running backtest: PolygonDataRetriever.__init__() takes from 1 to 2 positional arguments but 3 were given
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/spx_overnight_strategy/main.py", line 169, in main
    strategy = SPXOvernightStrategy(config=config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/spx_overnight_strategy/src/overnight_strategy.py", line 51, in __init__
    self.data_retriever = PolygonDataRetriever(api_key, self.config)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: PolygonDataRetriever.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-07-13 16:06:31,348 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:06:31,368 - __main__ - INFO - Starting backtest...
2025-07-13 16:06:31,369 - __main__ - ERROR - Backtest failed to complete
2025-07-13 16:07:19,771 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:07:19,792 - __main__ - INFO - Starting backtest...
2025-07-13 16:07:19,913 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:07:20,005 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 169
2025-07-13 16:07:20,138 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:07:20,270 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:07:20,400 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:07:20,400 - __main__ - ERROR - Backtest failed to complete
2025-07-13 16:08:29,895 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:08:29,916 - __main__ - INFO - Starting backtest...
2025-07-13 16:08:30,030 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:08:30,180 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:08:30,318 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:08:30,458 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:08:30,590 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:08:30,726 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:08:30,862 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 173
2025-07-13 16:08:30,999 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:08:31,134 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:08:31,137 - __main__ - ERROR - Error running backtest: 'final_capital'
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/spx_overnight_strategy/main.py", line 180, in main
    strategy.print_performance_summary()
  File "/Users/<USER>/Downloads/spx_overnight_strategy/src/overnight_strategy.py", line 432, in print_performance_summary
    print(f"Final Capital:        ${perf['final_capital']:,.2f}")
                                    ~~~~^^^^^^^^^^^^^^^^^
KeyError: 'final_capital'
2025-07-13 16:10:04,344 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:10:04,365 - __main__ - INFO - Starting backtest...
2025-07-13 16:10:04,478 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:10:04,558 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 169
2025-07-13 16:10:04,746 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 173
2025-07-13 16:10:04,877 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:10:05,011 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:10:05,144 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:10:05,283 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:10:05,415 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:10:05,549 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:10:05,550 - __main__ - ERROR - Error running backtest: 'SPXOvernightStrategy' object has no attribute 'get_performance_summary'
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/spx_overnight_strategy/main.py", line 189, in main
    summary = strategy.get_performance_summary()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SPXOvernightStrategy' object has no attribute 'get_performance_summary'. Did you mean: 'print_performance_summary'?
