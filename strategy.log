2025-07-13 16:04:51,835 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:04:51,836 - __main__ - ERROR - Error running backtest: SPXOvernightStrategy.__init__() got an unexpected keyword argument 'config'
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/spx_overnight_strategy/main.py", line 169, in main
    strategy = SPXOvernightStrategy(config=config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: SPXOvernightStrategy.__init__() got an unexpected keyword argument 'config'
2025-07-13 16:06:01,517 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:06:01,518 - __main__ - ERROR - Error running backtest: PolygonDataRetriever.__init__() takes from 1 to 2 positional arguments but 3 were given
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/spx_overnight_strategy/main.py", line 169, in main
    strategy = SPXOvernightStrategy(config=config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/spx_overnight_strategy/src/overnight_strategy.py", line 51, in __init__
    self.data_retriever = PolygonDataRetriever(api_key, self.config)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: PolygonDataRetriever.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-07-13 16:06:31,348 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:06:31,368 - __main__ - INFO - Starting backtest...
2025-07-13 16:06:31,369 - __main__ - ERROR - Backtest failed to complete
2025-07-13 16:07:19,771 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:07:19,792 - __main__ - INFO - Starting backtest...
2025-07-13 16:07:19,913 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:07:20,005 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 169
2025-07-13 16:07:20,138 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:07:20,270 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:07:20,400 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:07:20,400 - __main__ - ERROR - Backtest failed to complete
2025-07-13 16:08:29,895 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:08:29,916 - __main__ - INFO - Starting backtest...
2025-07-13 16:08:30,030 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:08:30,180 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:08:30,318 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:08:30,458 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:08:30,590 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:08:30,726 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:08:30,862 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 173
2025-07-13 16:08:30,999 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:08:31,134 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:08:31,137 - __main__ - ERROR - Error running backtest: 'final_capital'
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/spx_overnight_strategy/main.py", line 180, in main
    strategy.print_performance_summary()
  File "/Users/<USER>/Downloads/spx_overnight_strategy/src/overnight_strategy.py", line 432, in print_performance_summary
    print(f"Final Capital:        ${perf['final_capital']:,.2f}")
                                    ~~~~^^^^^^^^^^^^^^^^^
KeyError: 'final_capital'
2025-07-13 16:10:04,344 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:10:04,365 - __main__ - INFO - Starting backtest...
2025-07-13 16:10:04,478 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:10:04,558 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 169
2025-07-13 16:10:04,746 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 173
2025-07-13 16:10:04,877 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:10:05,011 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:10:05,144 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:10:05,283 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:10:05,415 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:10:05,549 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:10:05,550 - __main__ - ERROR - Error running backtest: 'SPXOvernightStrategy' object has no attribute 'get_performance_summary'
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/spx_overnight_strategy/main.py", line 189, in main
    summary = strategy.get_performance_summary()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SPXOvernightStrategy' object has no attribute 'get_performance_summary'. Did you mean: 'print_performance_summary'?
2025-07-13 16:12:05,018 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:12:05,099 - __main__ - INFO - Starting backtest...
2025-07-13 16:12:05,229 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:12:05,314 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:12:05,445 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:12:05,576 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:12:05,577 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:12:05,579 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:12:05,710 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:12:05,838 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:12:05,971 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:12:05,971 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:12:05,972 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:12:06,102 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:12:06,234 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:12:06,364 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:12:06,365 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:12:06,365 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:12:06,500 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:12:06,634 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:12:06,763 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:12:06,764 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:12:06,764 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:12:06,765 - position_manager - WARNING - No trade history to export
2025-07-13 16:12:06,769 - __main__ - INFO - Backtest completed - Total Return: 0.00%
2025-07-13 16:13:05,157 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:13:05,282 - __main__ - INFO - Starting backtest...
2025-07-13 16:13:05,418 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:13:05,533 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 167
2025-07-13 16:13:05,752 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 173
2025-07-13 16:13:05,964 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:05,964 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:05,973 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:13:06,113 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:13:06,251 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:13:06,384 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 98
2025-07-13 16:13:06,384 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:06,385 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:13:06,517 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:13:06,653 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:13:06,788 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:06,789 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:06,790 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:13:06,918 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:13:07,051 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:13:07,175 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:07,175 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:07,176 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:13:07,176 - position_manager - WARNING - No trade history to export
2025-07-13 16:13:07,182 - __main__ - INFO - Backtest completed - Total Return: 0.00%
2025-07-13 16:13:33,885 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:13:33,907 - __main__ - INFO - Starting backtest...
2025-07-13 16:13:34,017 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:13:34,088 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:13:34,217 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:13:34,350 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:34,351 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:34,353 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:13:34,353 - position_manager - INFO - Opened position: O:SPX240119C04750000 4 contracts @ $83.58
2025-07-13 16:13:34,486 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:13:34,618 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/O:SPX240119C04750000/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 211
2025-07-13 16:13:34,619 - position_manager - INFO - Closed position: O:SPX240119C04750000 P&L: $-22553.21
2025-07-13 16:13:34,754 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:13:34,886 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:13:35,013 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:35,013 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:35,015 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:13:35,015 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:13:35,142 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:13:35,274 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:13:35,404 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:35,405 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:35,406 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:13:35,406 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:13:35,533 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:13:35,666 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:13:35,794 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:13:35,794 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:13:35,795 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:13:35,795 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:13:35,801 - position_manager - INFO - Trade history exported to report/trade_history.csv
2025-07-13 16:13:35,802 - __main__ - INFO - Backtest completed - Total Return: -22.55%
2025-07-13 16:14:26,619 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:14:26,648 - __main__ - INFO - Starting backtest...
2025-07-13 16:14:27,173 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:27,174 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:14:27,175 - position_manager - INFO - Opened position: O:SPX240119C04750000 2 contracts @ $83.58
2025-07-13 16:14:27,439 - position_manager - INFO - Closed position: O:SPX240119C04750000 P&L: $-11276.60
2025-07-13 16:14:27,843 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:27,844 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:14:27,844 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:28,241 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:28,242 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:14:28,243 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:28,641 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:28,642 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:14:28,642 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:29,045 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:29,046 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04775000 (strike: $4775.00, price: $63.89)
2025-07-13 16:14:29,046 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:29,442 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:29,444 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $69.44)
2025-07-13 16:14:29,444 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:29,836 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:29,836 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04775000 (strike: $4775.00, price: $67.28)
2025-07-13 16:14:29,837 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:30,228 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:30,229 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04775000 (strike: $4775.00, price: $61.79)
2025-07-13 16:14:30,229 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:30,624 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:14:30,625 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04775000 (strike: $4775.00, price: $59.76)
2025-07-13 16:14:30,625 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:14:30,889 - data_retrieval - WARNING - No SPX price data available for 2024-01-15
2025-07-13 16:14:30,894 - position_manager - INFO - Trade history exported to report/trade_history.csv
2025-07-13 16:14:30,895 - __main__ - INFO - Backtest completed - Total Return: -22.55%
2025-07-13 16:23:55,735 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:24:04,312 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:24:04,335 - __main__ - INFO - Starting backtest...
2025-07-13 16:24:04,448 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:24:04,537 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:24:04,663 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-02/2024-01-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:24:04,793 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:24:04,793 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:04,794 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:24:04,794 - position_manager - INFO - Opened position: O:SPX240119C04750000 4 contracts @ $83.58
2025-07-13 16:24:04,925 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:24:05,062 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/O:SPX240119C04750000/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 211
2025-07-13 16:24:05,062 - position_manager - INFO - Closed position: O:SPX240119C04750000 P&L: $-22553.21
2025-07-13 16:24:05,187 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:24:05,312 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-03/2024-01-03?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 173
2025-07-13 16:24:05,459 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:24:05,460 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:05,460 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:24:05,460 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:24:05,588 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 171
2025-07-13 16:24:05,716 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-04/2024-01-04?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:24:05,846 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:24:05,847 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:05,847 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:24:05,847 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:24:05,999 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:24:06,148 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-01-05/2024-01-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:24:06,278 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-01-19&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:24:06,278 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:06,278 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:24:06,278 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:24:06,282 - position_manager - INFO - Trade history exported to report/trade_history.csv
2025-07-13 16:24:06,283 - __main__ - INFO - Backtest completed - Total Return: -22.55%
2025-07-13 16:24:17,711 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:24:17,727 - __main__ - INFO - Starting backtest...
2025-07-13 16:24:18,186 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:18,187 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:24:18,187 - position_manager - INFO - Opened position: O:SPX240119C04750000 2 contracts @ $83.58
2025-07-13 16:24:18,445 - position_manager - INFO - Closed position: O:SPX240119C04750000 P&L: $-11276.60
2025-07-13 16:24:18,838 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:18,839 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:24:18,839 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:24:19,242 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:19,242 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:24:19,243 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:24:19,636 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:19,637 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:24:19,637 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:24:20,025 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:20,026 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04775000 (strike: $4775.00, price: $63.89)
2025-07-13 16:24:20,026 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:24:20,416 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:20,416 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $69.44)
2025-07-13 16:24:20,416 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:24:20,810 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:20,811 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04775000 (strike: $4775.00, price: $67.28)
2025-07-13 16:24:20,811 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:24:21,191 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:21,192 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04775000 (strike: $4775.00, price: $61.79)
2025-07-13 16:24:21,192 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:24:21,583 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:24:21,584 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04775000 (strike: $4775.00, price: $59.76)
2025-07-13 16:24:21,584 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:24:21,845 - data_retrieval - WARNING - No SPX price data available for 2024-01-15
2025-07-13 16:24:21,847 - position_manager - INFO - Trade history exported to report/trade_history.csv
2025-07-13 16:24:21,848 - __main__ - INFO - Backtest completed - Total Return: -22.55%
2025-07-13 16:26:56,169 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:26:56,205 - __main__ - INFO - Starting backtest...
2025-07-13 16:26:56,719 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:26:56,721 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:26:56,721 - position_manager - INFO - Opened position: O:SPX240119C04750000 1 contracts @ $83.58
2025-07-13 16:26:56,991 - position_manager - INFO - Closed position: O:SPX240119C04750000 P&L: $-5638.30
2025-07-13 16:26:57,390 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:26:57,391 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:26:57,392 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:26:57,788 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:26:57,790 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:26:57,790 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:26:58,188 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:26:58,189 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:26:58,190 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:26:58,196 - position_manager - INFO - Trade history exported to report/trade_history.csv
2025-07-13 16:26:58,199 - __main__ - INFO - Backtest completed - Total Return: -22.55%
2025-07-13 16:28:40,655 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:28:40,677 - __main__ - INFO - Starting backtest...
2025-07-13 16:28:40,786 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.polygon.io:443
2025-07-13 16:28:40,866 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-02-01/2024-02-01?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 170
2025-07-13 16:28:40,997 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-02-01/2024-02-01?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 176
2025-07-13 16:28:41,124 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-02-16&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:28:41,124 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:28:41,125 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C04900000 (strike: $4900.00, price: $87.61)
2025-07-13 16:28:41,125 - position_manager - INFO - Opened position: O:SPX240216C04900000 3 contracts @ $87.61
2025-07-13 16:28:41,256 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-02-02/2024-02-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:28:41,385 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/O:SPX240216C04900000/range/1/day/2024-02-02/2024-02-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 207
2025-07-13 16:28:41,385 - position_manager - INFO - Closed position: O:SPX240216C04900000 P&L: $-2210.68
2025-07-13 16:28:41,522 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-02-02/2024-02-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 167
2025-07-13 16:28:41,652 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-02-02/2024-02-02?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 173
2025-07-13 16:28:41,780 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-02-16&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:28:41,780 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:28:41,780 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C04950000 (strike: $4950.00, price: $86.72)
2025-07-13 16:28:41,780 - position_manager - INFO - Opened position: O:SPX240216C04950000 3 contracts @ $86.72
2025-07-13 16:28:41,906 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-02-05/2024-02-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 175
2025-07-13 16:28:42,043 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/O:SPX240216C04950000/range/1/day/2024-02-05/2024-02-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 212
2025-07-13 16:28:42,043 - position_manager - INFO - Closed position: O:SPX240216C04950000 P&L: $-15624.67
2025-07-13 16:28:42,170 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:VIX/range/1/day/2024-02-05/2024-02-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 174
2025-07-13 16:28:42,301 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-02-05/2024-02-05?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 172
2025-07-13 16:28:42,427 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v3/reference/options/contracts?underlying_ticker=I%3ASPX&contract_type=call&limit=250&expiration_date=2024-02-16&apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 100
2025-07-13 16:28:42,427 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:28:42,427 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C04950000 (strike: $4950.00, price: $68.59)
2025-07-13 16:28:42,428 - position_manager - INFO - Opened position: O:SPX240216C04950000 4 contracts @ $68.59
2025-07-13 16:28:42,553 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/I:SPX/range/1/day/2024-02-06/2024-02-06?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 177
2025-07-13 16:28:42,691 - urllib3.connectionpool - DEBUG - https://api.polygon.io:443 "GET /v2/aggs/ticker/O:SPX240216C04950000/range/1/day/2024-02-06/2024-02-06?apikey=NH9F7BdmxCxaXnesitTCtq9MsrXZscOW HTTP/1.1" 200 209
2025-07-13 16:28:42,691 - position_manager - INFO - Closed position: O:SPX240216C04950000 P&L: $-12876.29
2025-07-13 16:28:42,693 - position_manager - INFO - Trade history exported to report/trade_history.csv
2025-07-13 16:28:42,694 - __main__ - INFO - Backtest completed - Total Return: -30.71%
2025-07-13 16:30:48,504 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:30:48,528 - __main__ - INFO - Starting backtest...
2025-07-13 16:30:49,016 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:30:49,016 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:30:49,017 - position_manager - INFO - Opened position: O:SPX240119C04750000 2 contracts @ $83.58
2025-07-13 16:30:49,279 - position_manager - INFO - Closed position: O:SPX240119C04750000 P&L: $-11276.60
2025-07-13 16:30:49,676 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:30:49,679 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:30:49,679 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:30:50,097 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:30:50,098 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:30:50,098 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:30:50,497 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:30:50,498 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:30:50,498 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:30:50,506 - position_manager - INFO - Trade history exported to report/trade_history.csv
2025-07-13 16:30:50,512 - __main__ - INFO - Backtest completed - Total Return: -22.55%
2025-07-13 16:31:59,067 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:31:59,089 - __main__ - INFO - Starting backtest...
2025-07-13 16:31:59,563 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:31:59,564 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:31:59,564 - position_manager - INFO - Opened position: O:SPX240119C04750000 2 contracts @ $83.58
2025-07-13 16:31:59,830 - position_manager - INFO - Closed position: O:SPX240119C04750000 P&L: $-11276.60
2025-07-13 16:32:00,228 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:32:00,228 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:32:00,229 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:32:00,635 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:32:00,635 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:32:00,636 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:32:01,045 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:32:01,046 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:32:01,046 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:32:01,055 - position_manager - INFO - Trade history exported to report/trade_history.csv
2025-07-13 16:32:01,968 - matplotlib.category - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:32:01,968 - matplotlib.category - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:32:02,851 - __main__ - WARNING - PDF generation failed: 'PerformanceAnalyzer' object has no attribute 'generate_pdf_report'
2025-07-13 16:32:02,851 - __main__ - INFO - Backtest completed - Total Return: -22.55%
2025-07-13 16:32:47,243 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:32:47,258 - __main__ - INFO - Starting backtest...
2025-07-13 16:32:47,695 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:32:47,696 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04750000 (strike: $4750.00, price: $83.58)
2025-07-13 16:32:47,696 - position_manager - INFO - Opened position: O:SPX240119C04750000 3 contracts @ $83.58
2025-07-13 16:32:47,965 - position_manager - INFO - Closed position: O:SPX240119C04750000 P&L: $-16914.91
2025-07-13 16:32:48,357 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:32:48,357 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $86.24)
2025-07-13 16:32:48,358 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:32:48,758 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:32:48,759 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $75.01)
2025-07-13 16:32:48,759 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:32:49,152 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:32:49,152 - synthetic_options - INFO - Generated synthetic call option: O:SPX240119C04700000 (strike: $4700.00, price: $76.51)
2025-07-13 16:32:49,153 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 22.6%
2025-07-13 16:32:49,155 - position_manager - INFO - Trade history exported to report/trade_history.csv
2025-07-13 16:32:50,101 - matplotlib.category - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:32:50,101 - matplotlib.category - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:32:52,664 - __main__ - INFO - Backtest completed - Total Return: -22.55%
2025-07-13 16:33:07,540 - config_manager - INFO - Configuration loaded and validated successfully
2025-07-13 16:33:07,561 - __main__ - INFO - Starting backtest...
2025-07-13 16:33:08,012 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:33:08,012 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C04900000 (strike: $4900.00, price: $87.61)
2025-07-13 16:33:08,012 - position_manager - INFO - Opened position: O:SPX240216C04900000 3 contracts @ $87.61
2025-07-13 16:33:08,269 - position_manager - INFO - Closed position: O:SPX240216C04900000 P&L: $-2210.68
2025-07-13 16:33:08,659 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:33:08,659 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C04950000 (strike: $4950.00, price: $86.72)
2025-07-13 16:33:08,659 - position_manager - INFO - Opened position: O:SPX240216C04950000 3 contracts @ $86.72
2025-07-13 16:33:08,921 - position_manager - INFO - Closed position: O:SPX240216C04950000 P&L: $-15624.67
2025-07-13 16:33:09,307 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:33:09,308 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C04950000 (strike: $4950.00, price: $68.59)
2025-07-13 16:33:09,308 - position_manager - INFO - Opened position: O:SPX240216C04950000 4 contracts @ $68.59
2025-07-13 16:33:09,568 - position_manager - INFO - Closed position: O:SPX240216C04950000 P&L: $-12876.29
2025-07-13 16:33:09,959 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:33:09,959 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C04950000 (strike: $4950.00, price: $71.00)
2025-07-13 16:33:09,959 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 30.7%
2025-07-13 16:33:10,354 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:33:10,355 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C05000000 (strike: $5000.00, price: $63.18)
2025-07-13 16:33:10,355 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 30.7%
2025-07-13 16:33:10,743 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:33:10,743 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C05000000 (strike: $5000.00, price: $60.72)
2025-07-13 16:33:10,744 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 30.7%
2025-07-13 16:33:11,125 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:33:11,126 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C05025000 (strike: $5025.00, price: $58.78)
2025-07-13 16:33:11,126 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 30.7%
2025-07-13 16:33:11,514 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:33:11,514 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C05025000 (strike: $5025.00, price: $41.74)
2025-07-13 16:33:11,515 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 30.7%
2025-07-13 16:33:11,907 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:33:11,907 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C04950000 (strike: $4950.00, price: $38.47)
2025-07-13 16:33:11,907 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 30.7%
2025-07-13 16:33:12,323 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:33:12,324 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C05000000 (strike: $5000.00, price: $30.53)
2025-07-13 16:33:12,324 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 30.7%
2025-07-13 16:33:12,711 - data_retrieval - WARNING - No options chain data available
2025-07-13 16:33:12,712 - synthetic_options - INFO - Generated synthetic call option: O:SPX240216C05025000 (strike: $5025.00, price: $23.81)
2025-07-13 16:33:12,712 - position_manager - WARNING - Cannot open position: Total loss limit exceeded: 30.7%
2025-07-13 16:33:12,714 - position_manager - INFO - Trade history exported to report/trade_history.csv
2025-07-13 16:33:13,717 - matplotlib.category - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:33:13,717 - matplotlib.category - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-13 16:33:15,538 - __main__ - INFO - Backtest completed - Total Return: -30.71%
