# SPX Overnight Options Trading Strategy

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code Style: Black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

A professional-grade SPX overnight options trading strategy with advanced risk management, comprehensive analytics, and production-ready architecture.

## 🚀 Features

### Core Strategy
- **SPX Overnight Options Trading** - Systematic overnight options strategy
- **Volatility Regime Analysis** - VIX-based market regime filtering
- **Advanced Risk Management** - Position sizing, stop-loss, and portfolio constraints
- **Option Pricing Engine** - Black-Scholes with Greeks calculation
- **Synthetic Options Generation** - For backtesting and analysis

### Professional Architecture
- **Centralized Configuration** - Environment variables and JSON config support
- **Comprehensive Error Handling** - Custom exceptions and robust logging
- **Modular Design** - Single responsibility principle throughout
- **Type Safety** - Complete type hints and validation
- **Production Ready** - Professional code quality and documentation

### Analytics & Reporting
- **Real-time P&L Tracking** - Portfolio performance monitoring
- **Advanced Metrics** - Sharpe ratio, max drawdown, win rate analysis
- **Comprehensive Reports** - PDF and interactive visualizations
- **Backtesting Engine** - Historical strategy performance analysis

## 📦 Installation

### Prerequisites
- Python 3.8 or higher
- Polygon.io API key (for market data)

### Setup
```bash
# Clone the repository
git clone https://github.com/petemcevoy/spx-overnight-strategy.git
cd spx-overnight-strategy

# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys and configuration
```

## 🔧 Configuration

### Environment Variables
Create a `.env` file with your configuration:
```bash
# API Configuration
POLYGON_API_KEY=your_polygon_api_key_here

# Strategy Parameters
STRATEGY_START_DATE=2024-01-01
STRATEGY_END_DATE=2024-12-31
INITIAL_CAPITAL=100000

# Risk Management
MAX_POSITION_SIZE=0.35
MAX_DAILY_LOSS=0.05
ENABLE_STOP_LOSS=false

# Volatility Filter
ENABLE_VIX_FILTER=true
VIX_LOW_THRESHOLD=12.0
VIX_HIGH_THRESHOLD=30.0
```

### JSON Configuration
Generate a sample configuration file:
```bash
cd src
python config_manager.py sample ../my_config.json
```

## 🚀 Usage

### Basic Usage
```bash
# Run with default configuration
python main.py

# Run with custom configuration
python main.py --config my_config.json

# Run with date range override
python main.py --start-date 2024-01-01 --end-date 2024-03-31

# Enable verbose logging
python main.py --verbose
```

### Configuration Management
```bash
# Validate configuration
python main.py --validate-config

# Print current configuration
python main.py --print-config

# Generate sample config
cd src && python config_manager.py sample ../sample.json
```

### Report Generation
```bash
# Generate comprehensive report
python generate_report.py

# Generate without PDF
python generate_report.py --no-pdf

# Custom output directory
python generate_report.py --output-dir ./custom_reports
```

## 🧪 Testing

Run the comprehensive test suite:
```bash
python test_refactored_code.py
```

Expected output:
```
🚀 Testing Refactored SPX Overnight Strategy Codebase
============================================================
🧪 Testing Configuration System...
  ✅ Configuration loads from environment
  ✅ Configuration validation passes
  ✅ ConfigurationManager works

🧪 Testing Data Structures...
  ✅ OptionsContract validation works
  ✅ MarketDataPoint validation works
  ✅ OptionsPosition P&L calculation works

🧪 Testing Option Pricing...
  ✅ Time to expiration calculation works
  ✅ Option price calculation works: $21.22
  ✅ Synthetic option generation works

🧪 Testing Position Management...
  ✅ Position size calculation works: 7 contracts
  ✅ Position opening works
  ✅ Portfolio summary works

🧪 Testing Volatility Filtering...
  ✅ Volatility regime determination works
  ✅ Trading decision works: True - Medium volatility regime

============================================================
📊 Test Results: 5/5 tests passed
🎉 All tests passed! The refactored codebase is working correctly.
```

## 📁 Project Structure

```
spx_overnight_strategy/
├── src/
│   ├── config.py                 # Configuration data classes
│   ├── config_manager.py         # Configuration management utilities
│   ├── data_retrieval.py         # Market data retrieval with error handling
│   ├── volatility_filter.py      # VIX-based volatility regime analysis
│   ├── option_pricing.py         # Black-Scholes pricing with Greeks
│   ├── position_manager.py       # Portfolio and risk management
│   ├── overnight_strategy.py     # Core strategy implementation
│   └── performance_analysis.py   # Performance metrics and analytics
├── main.py                       # Main strategy execution script
├── generate_report.py            # Report generation utility
├── test_refactored_code.py       # Comprehensive test suite
├── requirements.txt              # Python dependencies
├── sample_config.json            # Sample configuration file
├── API_DOCUMENTATION.md          # Detailed API documentation
├── REFACTORING_GUIDE.md          # Refactoring methodology guide
└── README.md                     # This file
```

## 📊 Strategy Overview

### Entry Conditions
- **Time**: 12:00 PM ET (Noon)
- **Strike Selection**: Out-of-the-money calls (50-75 points above SPX)
- **Volatility Filter**: Configurable VIX thresholds
- **Risk Constraints**: Position sizing based on portfolio risk

### Exit Conditions
- **Time**: 12:00 PM ET (Noon next trading day)
- **Stop Loss**: Optional configurable stop-loss levels
- **Risk Management**: Automatic position closure on risk limits

### Risk Management
- **Position Sizing**: Dynamic based on volatility and portfolio size
- **Daily Loss Limits**: Configurable maximum daily loss thresholds
- **Portfolio Constraints**: Maximum position concentration limits
- **Volatility Filtering**: Trade only in favorable market conditions

## 📈 Performance Metrics

The strategy tracks comprehensive performance metrics:

- **Returns**: Total return, annualized return, excess return
- **Risk Metrics**: Volatility, Sharpe ratio, Sortino ratio, maximum drawdown
- **Trade Analytics**: Win rate, average win/loss, profit factor
- **Greeks Exposure**: Delta, gamma, theta, vega portfolio exposure

## 🔗 API Documentation

For detailed API documentation, see [API_DOCUMENTATION.md](API_DOCUMENTATION.md).

## 📚 Additional Documentation

- **[Refactoring Guide](REFACTORING_GUIDE.md)** - Detailed refactoring methodology
- **[Refactoring Summary](REFACTORING_SUMMARY.md)** - Summary of improvements made
- **[API Documentation](API_DOCUMENTATION.md)** - Complete API reference

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This software is for educational and research purposes only. Trading options involves substantial risk and is not suitable for all investors. Past performance does not guarantee future results. Always consult with a qualified financial advisor before making investment decisions.

## 🙏 Acknowledgments

- **Polygon.io** for market data API
- **Black-Scholes Model** for option pricing methodology
- **Python Community** for excellent libraries and tools

---

**Built with ❤️ for systematic options trading**

