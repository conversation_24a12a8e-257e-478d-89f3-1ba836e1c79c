# SPX Overnight Options Strategy

A Python-based backtesting framework for an overnight SPX options trading strategy that buys at-the-money (ATM) call options at 3:30 PM and sells them at 10:00 AM the following trading day.

## Strategy Overview

- **Underlying**: S&P 500 Index (SPX)
- **Option Type**: Call Options (ATM)
- **Entry Time**: 3:30 PM ET
- **Exit Time**: 10:00 AM ET (next trading day)
- **Holding Period**: Overnight (~18.5 hours)
- **Data Source**: Polygon.io API

## Project Structure

```
spx_overnight_strategy/
├── src/                          # Source code
│   ├── data_retrieval.py        # Polygon.io API interface
│   ├── position_manager.py      # Position tracking and P&L
│   ├── overnight_strategy.py    # Main strategy implementation
│   ├── option_pricing.py        # Black-Scholes pricing for synthetic data
│   └── performance_analysis.py  # Analysis and visualization
├── data/                        # Data storage (empty initially)
├── report/                      # Generated reports and analysis
│   ├── performance_report.md    # Comprehensive analysis report
│   ├── performance_report.pdf   # PDF version of report
│   ├── equity_curve.png         # Equity curve visualization
│   ├── pnl_distribution.png     # P&L distribution charts
│   ├── trade_analysis.png       # Trade analysis charts
│   ├── trade_history.csv        # Individual trade records
│   ├── daily_performance.csv    # Daily performance metrics
│   └── performance_summary.json # Summary statistics
├── main.py                      # Main backtest execution script
├── generate_report.py           # Report generation script
├── requirements.txt             # Python dependencies
├── .env                        # Environment configuration
├── .env.template               # Environment template
└── README.md                   # This file
```

## Installation

1. **Clone or extract the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   - Copy `.env.template` to `.env`
   - Add your Polygon.io API key to the `.env` file:
     ```
     POLYGON_API_KEY=your_api_key_here
     ```

## Configuration

Edit the `.env` file to customize strategy parameters:

```bash
# API Configuration
POLYGON_API_KEY=your_polygon_api_key_here

# Strategy Parameters
UNDERLYING_SYMBOL=SPX
ENTRY_TIME=15:30  # 3:30 PM ET
EXIT_TIME=10:00   # 10:00 AM ET
CONTRACT_TYPE=call

# Backtesting Parameters
START_DATE=2024-01-01
END_DATE=2024-01-31
INITIAL_CAPITAL=100000
```

## Usage

### Running a Backtest

```bash
python main.py
```

This will:
- Load configuration from `.env` file
- Execute the overnight options strategy
- Generate trade history and performance metrics
- Save results to the `report/` directory

### Generating Analysis Report

```bash
python generate_report.py
```

This creates:
- Comprehensive performance report (Markdown and PDF)
- Equity curve visualization
- P&L distribution charts
- Trade analysis visualizations

## Key Features

### Data Handling
- **Real-time Data**: Uses Polygon.io API for SPX price data
- **Synthetic Options**: Black-Scholes pricing when market data unavailable
- **Historical Backtesting**: Supports custom date ranges

### Risk Management
- **Position Sizing**: Automatic position sizing based on capital allocation
- **Risk Limits**: Configurable maximum position size and daily loss limits
- **Stop Loss**: Built-in risk management features

### Analysis & Reporting
- **Performance Metrics**: Comprehensive statistics including Sharpe ratio, Sortino ratio, Calmar ratio
- **Visualizations**: Equity curves, P&L distributions, trade analysis charts
- **Export Options**: CSV, JSON, and PDF report formats

## Strategy Logic

1. **Daily Entry (3:30 PM ET)**:
   - Get current SPX price
   - Find next suitable expiration date (7-45 days out)
   - Identify ATM call option
   - Calculate position size based on risk management rules
   - Execute buy order

2. **Daily Exit (10:00 AM ET next day)**:
   - Get current option prices
   - Close all open positions
   - Calculate P&L and update performance metrics

3. **Option Pricing**:
   - Primary: Market data from Polygon.io
   - Fallback: Black-Scholes synthetic pricing
   - Handles both real-time and historical scenarios

## Performance Metrics

The system calculates comprehensive performance metrics:

- **Return Metrics**: Total return, annualized return
- **Risk Metrics**: Maximum drawdown, volatility, VaR
- **Efficiency Ratios**: Sharpe, Sortino, Calmar ratios
- **Trade Statistics**: Win rate, profit factor, average trade P&L
- **Advanced Analytics**: Consecutive wins/losses, holding periods

## Limitations & Considerations

1. **Data Limitations**: 
   - Historical options data may be limited
   - Uses synthetic pricing for missing data points

2. **Transaction Costs**: 
   - Does not include commissions or bid-ask spreads
   - Real trading costs would reduce returns

3. **Market Hours**: 
   - Assumes perfect execution at specified times
   - Real markets may have liquidity constraints

4. **Slippage**: 
   - Uses theoretical mid-prices
   - Actual execution may differ

## Customization

### Adding New Strategies
Extend the `SPXOvernightStrategy` class in `src/overnight_strategy.py`:

```python
class CustomStrategy(SPXOvernightStrategy):
    def execute_entry_signal(self, trade_date: str) -> bool:
        # Custom entry logic
        pass
    
    def execute_exit_signal(self, trade_date: str) -> int:
        # Custom exit logic
        pass
```

### Custom Risk Management
Modify `src/position_manager.py` to implement custom position sizing or risk rules.

### Additional Data Sources
Extend `src/data_retrieval.py` to add support for other data providers.

## Dependencies

- `pandas`: Data manipulation and analysis
- `numpy`: Numerical computations
- `matplotlib`: Plotting and visualization
- `seaborn`: Statistical visualization
- `scipy`: Scientific computing (Black-Scholes)
- `requests`: HTTP requests for API calls
- `python-dotenv`: Environment variable management
- `pytz`: Timezone handling

## API Requirements

- **Polygon.io Account**: Free tier available, paid plans for more data
- **API Key**: Required for accessing market data
- **Rate Limits**: Respects API rate limits (configurable delay)

## License

This project is for educational and research purposes. Please ensure compliance with your broker's terms of service and applicable regulations before using in live trading.

## Disclaimer

This software is for educational purposes only. Past performance does not guarantee future results. Trading options involves substantial risk and is not suitable for all investors. Always consult with a qualified financial advisor before making investment decisions.

## Support

For questions or issues:
1. Check the generated reports in the `report/` directory
2. Review the configuration in `.env` file
3. Ensure your Polygon.io API key is valid and has sufficient quota

---

*Generated by SPX Overnight Options Strategy Framework*

