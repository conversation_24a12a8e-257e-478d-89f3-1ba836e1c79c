#!/usr/bin/env python3
"""
Test script to verify the refactored SPX Overnight Strategy codebase
Tests core functionality without requiring API calls
"""

import sys
import os
from datetime import datetime, timedelta

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_configuration_system():
    """Test the configuration system"""
    print("🧪 Testing Configuration System...")
    
    try:
        from config_manager import ConfigurationManager
        from config import StrategyConfiguration
        
        # Test loading configuration
        config = StrategyConfiguration.load_from_env()
        print("  ✅ Configuration loads from environment")
        
        # Test validation
        errors = config.validate()
        if not errors:
            print("  ✅ Configuration validation passes")
        else:
            print(f"  ⚠️  Configuration validation warnings: {errors}")
        
        # Test configuration manager
        manager = ConfigurationManager()
        config2 = manager.load_configuration()
        print("  ✅ ConfigurationManager works")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Configuration system error: {e}")
        return False


def test_data_structures():
    """Test data structures and classes"""
    print("🧪 Testing Data Structures...")
    
    try:
        from data_retrieval import OptionsContract, MarketDataPoint
        from position_manager import OptionsPosition, TradeMetrics
        from volatility_filter import VolatilityRegime, TradingDecision
        
        # Test OptionsContract
        contract = OptionsContract(
            ticker="O:SPX240315C05000000",
            underlying_symbol="SPX",
            contract_type="call",
            strike_price=5000.0,
            expiration_date="2024-03-15"
        )
        
        if contract.is_valid():
            print("  ✅ OptionsContract validation works")
        else:
            print("  ❌ OptionsContract validation failed")
            return False
        
        # Test MarketDataPoint
        data_point = MarketDataPoint(
            timestamp=datetime.now(),
            price=5000.0,
            symbol="SPX"
        )
        
        if data_point.is_valid():
            print("  ✅ MarketDataPoint validation works")
        else:
            print("  ❌ MarketDataPoint validation failed")
            return False
        
        # Test OptionsPosition
        position = OptionsPosition(
            ticker="O:SPX240315C05000000",
            contract_type="call",
            strike_price=5000.0,
            expiration_date="2024-03-15",
            entry_date="2024-03-14",
            entry_time="15:30",
            entry_price=50.0,
            quantity=1,
            underlying_price_entry=4950.0
        )
        
        pnl = position.calculate_pnl(60.0)  # Exit at $60
        if pnl == 1000.0:  # (60-50) * 1 * 100
            print("  ✅ OptionsPosition P&L calculation works")
        else:
            print(f"  ❌ OptionsPosition P&L calculation failed: expected 1000, got {pnl}")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Data structures error: {e}")
        return False


def test_option_pricing():
    """Test option pricing functionality"""
    print("🧪 Testing Option Pricing...")
    
    try:
        from option_pricing import BlackScholesCalculator, SyntheticOptionsGenerator
        from config import StrategyConfiguration
        
        config = StrategyConfiguration.load_from_env()
        
        # Test Black-Scholes calculator
        bs_calc = BlackScholesCalculator(config)
        
        # Test time to expiration calculation
        current_date = "2024-03-14"
        expiration_date = "2024-03-15"
        time_to_exp = bs_calc.calculate_time_to_expiration(current_date, expiration_date)
        
        if 0 < time_to_exp < 1:  # Should be a fraction of a year
            print("  ✅ Time to expiration calculation works")
        else:
            print(f"  ❌ Time to expiration calculation failed: {time_to_exp}")
            return False
        
        # Test option price calculation
        option_price = bs_calc.calculate_option_price(
            underlying_price=5000.0,
            strike_price=5000.0,
            current_date=current_date,
            expiration_date=expiration_date,
            option_type="call"
        )
        
        if option_price > 0:
            print(f"  ✅ Option price calculation works: ${option_price:.2f}")
        else:
            print(f"  ❌ Option price calculation failed: {option_price}")
            return False
        
        # Test synthetic options generator
        syn_gen = SyntheticOptionsGenerator(config)
        
        atm_option = syn_gen.generate_atm_option(
            underlying_price=5000.0,
            current_date=current_date,
            expiration_date=expiration_date,
            option_type="call"
        )
        
        if atm_option and 'synthetic_price' in atm_option:
            print("  ✅ Synthetic option generation works")
        else:
            print("  ❌ Synthetic option generation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Option pricing error: {e}")
        return False


def test_position_management():
    """Test position management functionality"""
    print("🧪 Testing Position Management...")
    
    try:
        from position_manager import PositionManager
        from config import StrategyConfiguration
        
        config = StrategyConfiguration.load_from_env()
        
        # Test position manager
        pm = PositionManager(config)
        
        # Test position size calculation
        position_size = pm.calculate_position_size(
            option_price=50.0,
            underlying_price=5000.0
        )
        
        if position_size > 0:
            print(f"  ✅ Position size calculation works: {position_size} contracts")
        else:
            print(f"  ❌ Position size calculation failed: {position_size}")
            return False
        
        # Test position opening
        success = pm.open_position(
            ticker="O:SPX240315C05000000",
            contract_type="call",
            strike_price=5000.0,
            expiration_date="2024-03-15",
            entry_date="2024-03-14",
            entry_time="15:30",
            entry_price=50.0,
            underlying_price=4950.0,
            quantity=1
        )
        
        if success:
            print("  ✅ Position opening works")
        else:
            print("  ❌ Position opening failed")
            return False
        
        # Test portfolio summary
        summary = pm.get_portfolio_summary()
        if isinstance(summary, dict) and 'total_trades' in summary:
            print("  ✅ Portfolio summary works")
        else:
            print("  ❌ Portfolio summary failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Position management error: {e}")
        return False


def test_volatility_filtering():
    """Test volatility filtering functionality"""
    print("🧪 Testing Volatility Filtering...")
    
    try:
        from volatility_filter import VolatilityRegimeFilter, VolatilityRegime, TradingDecision
        from config import StrategyConfiguration
        
        config = StrategyConfiguration.load_from_env()
        
        # Create a mock data retriever for testing
        class MockDataRetriever:
            def get_vix_data(self, date):
                return 15.0  # Mock VIX value
        
        mock_retriever = MockDataRetriever()
        
        # Test volatility filter
        vf = VolatilityRegimeFilter(mock_retriever, config)
        
        # Test regime determination
        vol_data = vf.determine_volatility_regime("2024-03-14")
        
        if vol_data.regime == VolatilityRegime.MEDIUM:  # VIX 15 should be medium
            print("  ✅ Volatility regime determination works")
        else:
            print(f"  ❌ Volatility regime determination failed: {vol_data.regime}")
            return False
        
        # Test trading decision
        decision = vf.should_trade("2024-03-14")
        
        if isinstance(decision, TradingDecision):
            print(f"  ✅ Trading decision works: {decision.should_trade} - {decision.reason}")
        else:
            print("  ❌ Trading decision failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Volatility filtering error: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Testing Refactored SPX Overnight Strategy Codebase")
    print("=" * 60)
    
    tests = [
        test_configuration_system,
        test_data_structures,
        test_option_pricing,
        test_position_management,
        test_volatility_filtering
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
            print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The refactored codebase is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
