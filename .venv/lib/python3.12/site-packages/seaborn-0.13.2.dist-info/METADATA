Metadata-Version: 2.1
Name: seaborn
Version: 0.13.2
Summary: Statistical data visualization
Author-email: <PERSON> <<EMAIL>>
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Classifier: Intended Audience :: Science/Research
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: License :: OSI Approved :: BSD License
Classifier: Topic :: Scientific/Engineering :: Visualization
Classifier: Topic :: Multimedia :: Graphics
Classifier: Operating System :: OS Independent
Classifier: Framework :: Matplotlib
Requires-Dist: numpy>=1.20,!=1.24.0
Requires-Dist: pandas>=1.2
Requires-Dist: matplotlib>=3.4,!=3.6.1
Requires-Dist: pytest ; extra == "dev"
Requires-Dist: pytest-cov ; extra == "dev"
Requires-Dist: pytest-xdist ; extra == "dev"
Requires-Dist: flake8 ; extra == "dev"
Requires-Dist: mypy ; extra == "dev"
Requires-Dist: pandas-stubs ; extra == "dev"
Requires-Dist: pre-commit ; extra == "dev"
Requires-Dist: flit ; extra == "dev"
Requires-Dist: numpydoc ; extra == "docs"
Requires-Dist: nbconvert ; extra == "docs"
Requires-Dist: ipykernel ; extra == "docs"
Requires-Dist: sphinx<6.0.0 ; extra == "docs"
Requires-Dist: sphinx-copybutton ; extra == "docs"
Requires-Dist: sphinx-issues ; extra == "docs"
Requires-Dist: sphinx-design ; extra == "docs"
Requires-Dist: pyyaml ; extra == "docs"
Requires-Dist: pydata_sphinx_theme==0.10.0rc2 ; extra == "docs"
Requires-Dist: scipy>=1.7 ; extra == "stats"
Requires-Dist: statsmodels>=0.12 ; extra == "stats"
Project-URL: Docs, http://seaborn.pydata.org
Project-URL: Source, https://github.com/mwaskom/seaborn
Provides-Extra: dev
Provides-Extra: docs
Provides-Extra: stats

<img src="https://raw.githubusercontent.com/mwaskom/seaborn/master/doc/_static/logo-wide-lightbg.svg"><br>

--------------------------------------

seaborn: statistical data visualization
=======================================

[![PyPI Version](https://img.shields.io/pypi/v/seaborn.svg)](https://pypi.org/project/seaborn/)
[![License](https://img.shields.io/pypi/l/seaborn.svg)](https://github.com/mwaskom/seaborn/blob/master/LICENSE.md)
[![DOI](https://joss.theoj.org/papers/10.21105/joss.03021/status.svg)](https://doi.org/10.21105/joss.03021)
[![Tests](https://github.com/mwaskom/seaborn/workflows/CI/badge.svg)](https://github.com/mwaskom/seaborn/actions)
[![Code Coverage](https://codecov.io/gh/mwaskom/seaborn/branch/master/graph/badge.svg)](https://codecov.io/gh/mwaskom/seaborn)

Seaborn is a Python visualization library based on matplotlib. It provides a high-level interface for drawing attractive statistical graphics.


Documentation
-------------

Online documentation is available at [seaborn.pydata.org](https://seaborn.pydata.org).

The docs include a [tutorial](https://seaborn.pydata.org/tutorial.html), [example gallery](https://seaborn.pydata.org/examples/index.html), [API reference](https://seaborn.pydata.org/api.html), [FAQ](https://seaborn.pydata.org/faq), and other useful information.

To build the documentation locally, please refer to [`doc/README.md`](doc/README.md).

Dependencies
------------

Seaborn supports Python 3.8+.

Installation requires [numpy](https://numpy.org/), [pandas](https://pandas.pydata.org/), and [matplotlib](https://matplotlib.org/). Some advanced statistical functionality requires [scipy](https://www.scipy.org/) and/or [statsmodels](https://www.statsmodels.org/).


Installation
------------

The latest stable release (and required dependencies) can be installed from PyPI:

    pip install seaborn

It is also possible to include optional statistical dependencies:

    pip install seaborn[stats]

Seaborn can also be installed with conda:

    conda install seaborn

Note that the main anaconda repository lags PyPI in adding new releases, but conda-forge (`-c conda-forge`) typically updates quickly.

Citing
------

A paper describing seaborn has been published in the [Journal of Open Source Software](https://joss.theoj.org/papers/10.21105/joss.03021). The paper provides an introduction to the key features of the library, and it can be used as a citation if seaborn proves integral to a scientific publication.

Testing
-------

Testing seaborn requires installing additional dependencies; they can be installed with the `dev` extra (e.g., `pip install .[dev]`).

To test the code, run `make test` in the source directory. This will exercise the unit tests (using [pytest](https://docs.pytest.org/)) and generate a coverage report.

Code style is enforced with `flake8` using the settings in the [`setup.cfg`](./setup.cfg) file. Run `make lint` to check. Alternately, you can use `pre-commit` to automatically run lint checks on any files you are committing: just run `pre-commit install` to set it up, and then commit as usual going forward.

Development
-----------

Seaborn development takes place on Github: https://github.com/mwaskom/seaborn

Please submit bugs that you encounter to the [issue tracker](https://github.com/mwaskom/seaborn/issues) with a reproducible example demonstrating the problem. Questions about usage are more at home on StackOverflow, where there is a [seaborn tag](https://stackoverflow.com/questions/tagged/seaborn).

