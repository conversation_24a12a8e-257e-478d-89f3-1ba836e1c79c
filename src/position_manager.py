"""
Position Manager for SPX Overnight Options Strategy
Handles position tracking, P&L calculations, and risk management
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import json

@dataclass
class OptionsPosition:
    """
    Represents an options position
    """
    ticker: str
    contract_type: str  # 'call' or 'put'
    strike_price: float
    expiration_date: str
    entry_date: str
    entry_time: str
    entry_price: float
    quantity: int
    underlying_price_at_entry: float
    exit_date: Optional[str] = None
    exit_time: Optional[str] = None
    exit_price: Optional[float] = None
    underlying_price_at_exit: Optional[float] = None
    pnl: Optional[float] = None
    pnl_percent: Optional[float] = None
    status: str = 'open'  # 'open', 'closed', 'expired'
    
    def to_dict(self) -> Dict:
        """Convert position to dictionary"""
        return {
            'ticker': self.ticker,
            'contract_type': self.contract_type,
            'strike_price': self.strike_price,
            'expiration_date': self.expiration_date,
            'entry_date': self.entry_date,
            'entry_time': self.entry_time,
            'entry_price': self.entry_price,
            'quantity': self.quantity,
            'underlying_price_at_entry': self.underlying_price_at_entry,
            'exit_date': self.exit_date,
            'exit_time': self.exit_time,
            'exit_price': self.exit_price,
            'underlying_price_at_exit': self.underlying_price_at_exit,
            'pnl': self.pnl,
            'pnl_percent': self.pnl_percent,
            'status': self.status
        }

class PositionManager:
    """
    Manages options positions for the overnight strategy
    """
    
    def __init__(self, initial_capital: float = 100000):
        """
        Initialize position manager
        
        Args:
            initial_capital: Starting capital for the strategy
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions: List[OptionsPosition] = []
        self.trade_history: List[Dict] = []
        self.daily_pnl: List[Dict] = []
        
        # Risk management parameters - Adjusted for 60-day options
        self.max_position_size = 0.35  # 35% of capital per trade (higher for longer-dated options)
        self.max_daily_loss = 0.05     # 5% daily loss limit
        
    def calculate_position_size(self, option_price: float, 
                              underlying_price: float) -> int:
        """
        Calculate appropriate position size based on risk management rules
        
        Args:
            option_price: Price of the option
            underlying_price: Price of underlying asset
            
        Returns:
            Number of contracts to trade
        """
        # Calculate maximum position value based on risk limit
        max_position_value = self.current_capital * self.max_position_size
        
        # Each option contract represents 100 shares for SPX
        contract_value = option_price * 100
        
        if contract_value <= 0:
            return 0
        
        # Calculate number of contracts
        max_contracts = int(max_position_value / contract_value)
        
        # Ensure at least 1 contract if we have enough capital
        if max_contracts == 0 and contract_value <= max_position_value:
            max_contracts = 1
        
        return max_contracts
    
    def open_position(self, ticker: str, contract_type: str, strike_price: float,
                     expiration_date: str, entry_date: str, entry_time: str,
                     entry_price: float, underlying_price: float) -> bool:
        """
        Open a new options position
        
        Args:
            ticker: Options ticker symbol
            contract_type: 'call' or 'put'
            strike_price: Strike price of the option
            expiration_date: Expiration date (YYYY-MM-DD)
            entry_date: Entry date (YYYY-MM-DD)
            entry_time: Entry time (HH:MM)
            entry_price: Entry price of the option
            underlying_price: Price of underlying at entry
            
        Returns:
            True if position opened successfully
        """
        try:
            # Calculate position size
            quantity = self.calculate_position_size(entry_price, underlying_price)
            
            if quantity <= 0:
                print(f"Cannot open position: insufficient capital or invalid price")
                return False
            
            # Create position
            position = OptionsPosition(
                ticker=ticker,
                contract_type=contract_type,
                strike_price=strike_price,
                expiration_date=expiration_date,
                entry_date=entry_date,
                entry_time=entry_time,
                entry_price=entry_price,
                quantity=quantity,
                underlying_price_at_entry=underlying_price
            )
            
            # Add to positions list
            self.positions.append(position)
            
            # Update capital (subtract cost of position)
            position_cost = entry_price * quantity * 100  # 100 shares per contract
            self.current_capital -= position_cost
            
            print(f"Opened position: {quantity} contracts of {ticker} at ${entry_price:.2f}")
            
            return True
            
        except Exception as e:
            print(f"Error opening position: {e}")
            return False
    
    def close_position(self, position_index: int, exit_date: str, 
                      exit_time: str, exit_price: float, 
                      underlying_price: float) -> bool:
        """
        Close an existing position
        
        Args:
            position_index: Index of position in positions list
            exit_date: Exit date (YYYY-MM-DD)
            exit_time: Exit time (HH:MM)
            exit_price: Exit price of the option
            underlying_price: Price of underlying at exit
            
        Returns:
            True if position closed successfully
        """
        try:
            if position_index >= len(self.positions):
                print(f"Invalid position index: {position_index}")
                return False
            
            position = self.positions[position_index]
            
            if position.status != 'open':
                print(f"Position {position_index} is not open")
                return False
            
            # Update position with exit information
            position.exit_date = exit_date
            position.exit_time = exit_time
            position.exit_price = exit_price
            position.underlying_price_at_exit = underlying_price
            position.status = 'closed'
            
            # Calculate P&L
            entry_value = position.entry_price * position.quantity * 100
            exit_value = exit_price * position.quantity * 100
            position.pnl = exit_value - entry_value
            position.pnl_percent = (position.pnl / entry_value) * 100 if entry_value > 0 else 0
            
            # Update capital
            self.current_capital += exit_value
            
            # Add to trade history
            self.trade_history.append(position.to_dict())
            
            print(f"Closed position: {position.quantity} contracts of {position.ticker}")
            print(f"P&L: ${position.pnl:.2f} ({position.pnl_percent:.2f}%)")
            
            return True
            
        except Exception as e:
            print(f"Error closing position: {e}")
            return False
    
    def close_all_positions(self, exit_date: str, exit_time: str, 
                           exit_prices: Dict[str, float],
                           underlying_price: float) -> int:
        """
        Close all open positions
        
        Args:
            exit_date: Exit date (YYYY-MM-DD)
            exit_time: Exit time (HH:MM)
            exit_prices: Dictionary mapping ticker to exit price
            underlying_price: Price of underlying at exit
            
        Returns:
            Number of positions closed
        """
        closed_count = 0
        
        for i, position in enumerate(self.positions):
            if position.status == 'open':
                exit_price = exit_prices.get(position.ticker)
                if exit_price is not None:
                    if self.close_position(i, exit_date, exit_time, 
                                         exit_price, underlying_price):
                        closed_count += 1
        
        return closed_count
    
    def get_open_positions(self) -> List[OptionsPosition]:
        """Get all open positions"""
        return [pos for pos in self.positions if pos.status == 'open']
    
    def get_closed_positions(self) -> List[OptionsPosition]:
        """Get all closed positions"""
        return [pos for pos in self.positions if pos.status == 'closed']
    
    def calculate_total_pnl(self) -> float:
        """Calculate total P&L from all closed positions"""
        return sum(pos.pnl for pos in self.positions 
                  if pos.status == 'closed' and pos.pnl is not None)
    
    def calculate_win_rate(self) -> float:
        """Calculate win rate percentage"""
        closed_positions = self.get_closed_positions()
        if not closed_positions:
            return 0.0
        
        winning_trades = sum(1 for pos in closed_positions if pos.pnl > 0)
        return (winning_trades / len(closed_positions)) * 100
    
    def calculate_profit_factor(self) -> float:
        """Calculate profit factor (gross profit / gross loss)"""
        closed_positions = self.get_closed_positions()
        if not closed_positions:
            return 0.0
        
        gross_profit = sum(pos.pnl for pos in closed_positions if pos.pnl > 0)
        gross_loss = abs(sum(pos.pnl for pos in closed_positions if pos.pnl < 0))
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0.0
        
        return gross_profit / gross_loss
    
    def calculate_max_drawdown(self) -> Tuple[float, float]:
        """
        Calculate maximum drawdown
        
        Returns:
            Tuple of (max_drawdown_amount, max_drawdown_percent)
        """
        if not self.trade_history:
            return 0.0, 0.0
        
        # Calculate running capital
        running_capital = self.initial_capital
        peak_capital = self.initial_capital
        max_drawdown = 0.0
        max_drawdown_percent = 0.0
        
        for trade in self.trade_history:
            running_capital += trade['pnl']
            
            if running_capital > peak_capital:
                peak_capital = running_capital
            
            drawdown = peak_capital - running_capital
            drawdown_percent = (drawdown / peak_capital) * 100 if peak_capital > 0 else 0
            
            if drawdown > max_drawdown:
                max_drawdown = drawdown
                max_drawdown_percent = drawdown_percent
        
        return max_drawdown, max_drawdown_percent
    
    def get_performance_summary(self) -> Dict:
        """
        Get comprehensive performance summary
        
        Returns:
            Dictionary with performance metrics
        """
        closed_positions = self.get_closed_positions()
        total_pnl = self.calculate_total_pnl()
        win_rate = self.calculate_win_rate()
        profit_factor = self.calculate_profit_factor()
        max_dd, max_dd_pct = self.calculate_max_drawdown()
        
        return {
            'initial_capital': self.initial_capital,
            'final_capital': self.current_capital,
            'total_pnl': total_pnl,
            'total_return_percent': (total_pnl / self.initial_capital) * 100,
            'total_trades': len(closed_positions),
            'winning_trades': sum(1 for pos in closed_positions if pos.pnl > 0),
            'losing_trades': sum(1 for pos in closed_positions if pos.pnl < 0),
            'win_rate_percent': win_rate,
            'profit_factor': profit_factor,
            'max_drawdown': max_dd,
            'max_drawdown_percent': max_dd_pct,
            'average_trade_pnl': total_pnl / len(closed_positions) if closed_positions else 0,
            'largest_win': max((pos.pnl for pos in closed_positions if pos.pnl > 0), default=0),
            'largest_loss': min((pos.pnl for pos in closed_positions if pos.pnl < 0), default=0)
        }
    
    def export_trades_to_csv(self, filename: str) -> bool:
        """
        Export trade history to CSV file
        
        Args:
            filename: Output CSV filename
            
        Returns:
            True if export successful
        """
        try:
            if not self.trade_history:
                print("No trades to export")
                return False
            
            df = pd.DataFrame(self.trade_history)
            df.to_csv(filename, index=False)
            print(f"Exported {len(self.trade_history)} trades to {filename}")
            return True
            
        except Exception as e:
            print(f"Error exporting trades: {e}")
            return False

