"""
Refactored Position Manager for SPX Overnight Options Strategy
Handles position tracking, P&L calculations, and risk management
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, NamedTuple
from dataclasses import dataclass, field
import json
import logging
import uuid

from config import StrategyConfiguration
from config_manager import load_config


class PositionError(Exception):
    """Custom exception for position management errors"""
    pass


class TradeMetrics(NamedTuple):
    """Container for trade performance metrics"""
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    max_drawdown: float


class RiskMetrics(NamedTuple):
    """Container for risk management metrics"""
    current_exposure: float
    max_position_size: float
    daily_pnl: float
    total_pnl: float
    drawdown: float
    var_95: float  # Value at Risk 95%


@dataclass
class OptionsPosition:
    """
    Represents an options position with enhanced tracking
    """
    ticker: str
    contract_type: str  # 'call' or 'put'
    strike_price: float
    expiration_date: str
    entry_date: str
    entry_time: str
    entry_price: float
    quantity: int
    underlying_price_entry: float
    
    # Position tracking
    status: str = 'open'  # 'open', 'closed', 'expired'
    exit_date: Optional[str] = None
    exit_time: Optional[str] = None
    exit_price: Optional[float] = None
    underlying_price_exit: Optional[float] = None
    
    # P&L tracking
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    
    # Risk metrics
    max_loss: float = field(default=0.0)
    max_gain: float = field(default=0.0)
    
    # Trade metadata
    trade_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    notes: Optional[str] = None
    
    def calculate_pnl(self, current_price: Optional[float] = None) -> float:
        """
        Calculate current P&L for the position
        
        Args:
            current_price: Current option price (if None, uses exit_price for closed positions)
            
        Returns:
            Current P&L
        """
        if self.status == 'closed' and self.exit_price is not None:
            # Realized P&L for closed positions
            pnl = (self.exit_price - self.entry_price) * self.quantity * 100
            self.realized_pnl = pnl
            return pnl
        elif current_price is not None:
            # Unrealized P&L for open positions
            pnl = (current_price - self.entry_price) * self.quantity * 100
            self.unrealized_pnl = pnl
            
            # Update max gain/loss tracking
            self.max_gain = max(self.max_gain, pnl)
            self.max_loss = min(self.max_loss, pnl)
            
            return pnl
        else:
            return 0.0
    
    def get_position_value(self, current_price: Optional[float] = None) -> float:
        """
        Get current position value
        
        Args:
            current_price: Current option price
            
        Returns:
            Current position value
        """
        price = current_price or self.exit_price or self.entry_price
        return price * self.quantity * 100
    
    def get_return_percentage(self) -> float:
        """
        Get return percentage for the position
        
        Returns:
            Return percentage
        """
        if self.status == 'closed' and self.exit_price is not None:
            return ((self.exit_price - self.entry_price) / self.entry_price) * 100
        return 0.0
    
    def is_profitable(self) -> bool:
        """Check if position is profitable"""
        return self.calculate_pnl() > 0
    
    def get_days_held(self) -> int:
        """Get number of days position was held"""
        try:
            entry_dt = datetime.strptime(self.entry_date, '%Y-%m-%d')
            exit_dt = datetime.strptime(self.exit_date or datetime.now().strftime('%Y-%m-%d'), '%Y-%m-%d')
            return (exit_dt - entry_dt).days
        except:
            return 0
    
    def to_dict(self) -> Dict:
        """Convert position to dictionary for serialization"""
        return {
            'trade_id': self.trade_id,
            'ticker': self.ticker,
            'contract_type': self.contract_type,
            'strike_price': self.strike_price,
            'expiration_date': self.expiration_date,
            'entry_date': self.entry_date,
            'entry_time': self.entry_time,
            'entry_price': self.entry_price,
            'quantity': self.quantity,
            'underlying_price_entry': self.underlying_price_entry,
            'status': self.status,
            'exit_date': self.exit_date,
            'exit_time': self.exit_time,
            'exit_price': self.exit_price,
            'underlying_price_exit': self.underlying_price_exit,
            'realized_pnl': self.realized_pnl,
            'unrealized_pnl': self.unrealized_pnl,
            'max_loss': self.max_loss,
            'max_gain': self.max_gain,
            'notes': self.notes,
            'return_percentage': self.get_return_percentage(),
            'days_held': self.get_days_held()
        }


class PositionManager:
    """
    Enhanced position manager for the overnight strategy with risk management
    """
    
    def __init__(self, config: Optional[StrategyConfiguration] = None):
        """
        Initialize position manager
        
        Args:
            config: Strategy configuration. If None, loads from environment
        """
        self.config = config or load_config()
        self.strategy_config = self.config.strategy
        self.risk_config = self.config.risk_management
        
        # Portfolio state
        self.initial_capital = self.strategy_config.initial_capital
        self.current_capital = self.initial_capital
        self.positions: List[OptionsPosition] = []
        self.trade_history: List[Dict] = []
        self.daily_pnl: List[Dict] = []
        
        # Risk tracking
        self.peak_capital = self.initial_capital
        self.max_drawdown = 0.0
        
        # Set up logging
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """Set up logging for position manager"""
        logger = logging.getLogger('position_manager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def calculate_position_size(self, option_price: float, 
                              underlying_price: float) -> int:
        """
        Calculate appropriate position size based on risk management rules
        
        Args:
            option_price: Price of the option
            underlying_price: Price of underlying asset
            
        Returns:
            Number of contracts to trade
            
        Raises:
            PositionError: If position size calculation fails
        """
        try:
            if option_price <= 0:
                raise PositionError(f"Invalid option price: {option_price}")
            
            # Calculate maximum position value based on risk limit
            max_position_value = self.current_capital * self.risk_config.max_position_size
            
            # Each option contract represents contract_multiplier shares
            contract_value = option_price * self.risk_config.contract_multiplier
            
            # Calculate number of contracts
            max_contracts = int(max_position_value / contract_value)
            
            # Ensure at least 1 contract if we have enough capital
            if max_contracts == 0 and contract_value <= max_position_value:
                max_contracts = 1
            
            # Apply minimum position size constraint
            min_position_value = self.current_capital * self.risk_config.min_position_size
            min_contracts = max(1, int(min_position_value / contract_value))
            
            contracts = max(min_contracts, max_contracts)
            
            self.logger.debug(f"Position size calculation: {contracts} contracts "
                            f"(option: ${option_price:.2f}, max value: ${max_position_value:.2f})")
            
            return contracts
            
        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            raise PositionError(f"Position size calculation failed: {e}")
    
    def can_open_position(self, option_price: float) -> Tuple[bool, str]:
        """
        Check if a new position can be opened based on risk constraints
        
        Args:
            option_price: Price of the option
            
        Returns:
            Tuple of (can_open, reason)
        """
        try:
            # Check daily loss limit
            daily_pnl = self.get_daily_pnl()
            if daily_pnl < -abs(self.current_capital * self.risk_config.max_daily_loss):
                return False, f"Daily loss limit exceeded: ${daily_pnl:.2f}"
            
            # Check total loss limit
            total_loss = (self.initial_capital - self.current_capital) / self.initial_capital
            if total_loss > self.risk_config.max_total_loss:
                return False, f"Total loss limit exceeded: {total_loss:.1%}"
            
            # Check if we have enough capital for minimum position
            min_position_value = option_price * self.risk_config.contract_multiplier
            if min_position_value > self.current_capital * self.risk_config.max_position_size:
                return False, f"Insufficient capital for minimum position: ${min_position_value:.2f}"
            
            return True, "Position can be opened"
            
        except Exception as e:
            self.logger.error(f"Error checking position constraints: {e}")
            return False, f"Error checking constraints: {e}"

    def open_position(self, ticker: str, contract_type: str, strike_price: float,
                     expiration_date: str, entry_date: str, entry_time: str,
                     entry_price: float, underlying_price: float,
                     quantity: Optional[int] = None) -> bool:
        """
        Open a new options position

        Args:
            ticker: Option ticker symbol
            contract_type: 'call' or 'put'
            strike_price: Strike price
            expiration_date: Expiration date (YYYY-MM-DD)
            entry_date: Entry date (YYYY-MM-DD)
            entry_time: Entry time (HH:MM)
            entry_price: Entry price per contract
            underlying_price: Underlying price at entry
            quantity: Number of contracts (if None, calculated automatically)

        Returns:
            True if position opened successfully

        Raises:
            PositionError: If position opening fails
        """
        try:
            # Check if position can be opened
            can_open, reason = self.can_open_position(entry_price)
            if not can_open:
                self.logger.warning(f"Cannot open position: {reason}")
                return False

            # Calculate position size if not provided
            if quantity is None:
                quantity = self.calculate_position_size(entry_price, underlying_price)

            # Create new position
            position = OptionsPosition(
                ticker=ticker,
                contract_type=contract_type,
                strike_price=strike_price,
                expiration_date=expiration_date,
                entry_date=entry_date,
                entry_time=entry_time,
                entry_price=entry_price,
                quantity=quantity,
                underlying_price_entry=underlying_price
            )

            # Add to positions list
            self.positions.append(position)

            # Update capital (subtract premium paid)
            position_cost = entry_price * quantity * self.risk_config.contract_multiplier
            self.current_capital -= position_cost

            # Log the trade
            self.logger.info(f"Opened position: {ticker} {quantity} contracts @ ${entry_price:.2f}")

            return True

        except Exception as e:
            self.logger.error(f"Error opening position: {e}")
            raise PositionError(f"Failed to open position: {e}")

    def close_position(self, position_index: int, exit_date: str, exit_time: str,
                      exit_price: float, underlying_price: float) -> bool:
        """
        Close an existing position

        Args:
            position_index: Index of position in positions list
            exit_date: Exit date (YYYY-MM-DD)
            exit_time: Exit time (HH:MM)
            exit_price: Exit price per contract
            underlying_price: Underlying price at exit

        Returns:
            True if position closed successfully

        Raises:
            PositionError: If position closing fails
        """
        try:
            if position_index >= len(self.positions):
                raise PositionError(f"Invalid position index: {position_index}")

            position = self.positions[position_index]

            if position.status != 'open':
                self.logger.warning(f"Position {position.ticker} is not open")
                return False

            # Update position
            position.exit_date = exit_date
            position.exit_time = exit_time
            position.exit_price = exit_price
            position.underlying_price_exit = underlying_price
            position.status = 'closed'

            # Calculate P&L
            pnl = position.calculate_pnl()

            # Update capital (add proceeds from sale)
            position_proceeds = exit_price * position.quantity * self.risk_config.contract_multiplier
            self.current_capital += position_proceeds

            # Update peak capital and drawdown
            self.peak_capital = max(self.peak_capital, self.current_capital)
            current_drawdown = (self.peak_capital - self.current_capital) / self.peak_capital
            self.max_drawdown = max(self.max_drawdown, current_drawdown)

            # Add to trade history
            trade_record = position.to_dict()
            self.trade_history.append(trade_record)

            # Log the trade
            self.logger.info(f"Closed position: {position.ticker} P&L: ${pnl:.2f}")

            return True

        except Exception as e:
            self.logger.error(f"Error closing position: {e}")
            raise PositionError(f"Failed to close position: {e}")

    def close_all_positions(self, exit_date: str, exit_time: str,
                           exit_prices: Dict[str, float],
                           underlying_price: float) -> int:
        """
        Close all open positions

        Args:
            exit_date: Exit date (YYYY-MM-DD)
            exit_time: Exit time (HH:MM)
            exit_prices: Dictionary mapping ticker to exit price
            underlying_price: Price of underlying at exit

        Returns:
            Number of positions closed
        """
        closed_count = 0

        for i, position in enumerate(self.positions):
            if position.status == 'open':
                exit_price = exit_prices.get(position.ticker)
                if exit_price is not None:
                    try:
                        if self.close_position(i, exit_date, exit_time, exit_price, underlying_price):
                            closed_count += 1
                    except Exception as e:
                        self.logger.error(f"Failed to close position {position.ticker}: {e}")

        return closed_count

    def get_open_positions(self) -> List[OptionsPosition]:
        """Get all open positions"""
        return [pos for pos in self.positions if pos.status == 'open']

    def get_closed_positions(self) -> List[OptionsPosition]:
        """Get all closed positions"""
        return [pos for pos in self.positions if pos.status == 'closed']

    def get_daily_pnl(self, date: Optional[str] = None) -> float:
        """
        Get P&L for a specific date

        Args:
            date: Date to get P&L for (if None, uses today)

        Returns:
            Daily P&L
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')

        daily_pnl = 0.0
        for position in self.positions:
            if position.exit_date == date:
                daily_pnl += position.realized_pnl

        return daily_pnl

    def get_total_pnl(self) -> float:
        """Get total realized P&L"""
        return sum(pos.realized_pnl for pos in self.positions if pos.status == 'closed')

    def get_unrealized_pnl(self, current_prices: Dict[str, float]) -> float:
        """
        Get total unrealized P&L for open positions

        Args:
            current_prices: Dictionary mapping ticker to current price

        Returns:
            Total unrealized P&L
        """
        unrealized_pnl = 0.0
        for position in self.get_open_positions():
            current_price = current_prices.get(position.ticker)
            if current_price is not None:
                unrealized_pnl += position.calculate_pnl(current_price)

        return unrealized_pnl

    def calculate_trade_metrics(self) -> TradeMetrics:
        """
        Calculate comprehensive trade performance metrics

        Returns:
            TradeMetrics with performance statistics
        """
        closed_positions = self.get_closed_positions()

        if not closed_positions:
            return TradeMetrics(0, 0, 0, 0.0, 0.0, 0.0, 0.0, self.max_drawdown)

        total_trades = len(closed_positions)
        winning_trades = sum(1 for pos in closed_positions if pos.is_profitable())
        losing_trades = total_trades - winning_trades

        win_rate = winning_trades / total_trades if total_trades > 0 else 0.0

        wins = [pos.realized_pnl for pos in closed_positions if pos.is_profitable()]
        losses = [pos.realized_pnl for pos in closed_positions if not pos.is_profitable()]

        avg_win = np.mean(wins) if wins else 0.0
        avg_loss = np.mean(losses) if losses else 0.0

        gross_profit = sum(wins) if wins else 0.0
        gross_loss = abs(sum(losses)) if losses else 0.0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

        return TradeMetrics(
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            max_drawdown=self.max_drawdown
        )

    def get_risk_metrics(self, current_prices: Dict[str, float]) -> RiskMetrics:
        """
        Calculate current risk metrics

        Args:
            current_prices: Dictionary mapping ticker to current price

        Returns:
            RiskMetrics with current risk statistics
        """
        # Calculate current exposure
        current_exposure = 0.0
        for position in self.get_open_positions():
            current_price = current_prices.get(position.ticker, position.entry_price)
            current_exposure += position.get_position_value(current_price)

        # Calculate daily P&L
        daily_pnl = self.get_daily_pnl()

        # Calculate total P&L including unrealized
        total_pnl = self.get_total_pnl() + self.get_unrealized_pnl(current_prices)

        # Calculate current drawdown
        current_drawdown = (self.peak_capital - self.current_capital) / self.peak_capital

        # Calculate VaR (simplified - using historical returns)
        returns = [pos.get_return_percentage() for pos in self.get_closed_positions()]
        var_95 = np.percentile(returns, 5) if returns else 0.0

        return RiskMetrics(
            current_exposure=current_exposure,
            max_position_size=self.current_capital * self.risk_config.max_position_size,
            daily_pnl=daily_pnl,
            total_pnl=total_pnl,
            drawdown=current_drawdown,
            var_95=var_95
        )

    def export_trade_history(self, filename: str) -> None:
        """
        Export trade history to CSV file

        Args:
            filename: Output filename
        """
        try:
            if not self.trade_history:
                self.logger.warning("No trade history to export")
                return

            df = pd.DataFrame(self.trade_history)
            df.to_csv(filename, index=False)
            self.logger.info(f"Trade history exported to {filename}")

        except Exception as e:
            self.logger.error(f"Error exporting trade history: {e}")
            raise PositionError(f"Failed to export trade history: {e}")

    def get_portfolio_summary(self) -> Dict:
        """
        Get comprehensive portfolio summary

        Returns:
            Dictionary with portfolio statistics
        """
        trade_metrics = self.calculate_trade_metrics()

        return {
            'initial_capital': self.initial_capital,
            'current_capital': self.current_capital,
            'total_return': (self.current_capital - self.initial_capital) / self.initial_capital,
            'total_return_pct': ((self.current_capital - self.initial_capital) / self.initial_capital) * 100,
            'peak_capital': self.peak_capital,
            'max_drawdown': self.max_drawdown,
            'max_drawdown_pct': self.max_drawdown * 100,
            'total_trades': trade_metrics.total_trades,
            'winning_trades': trade_metrics.winning_trades,
            'losing_trades': trade_metrics.losing_trades,
            'win_rate': trade_metrics.win_rate,
            'win_rate_pct': trade_metrics.win_rate * 100,
            'avg_win': trade_metrics.avg_win,
            'avg_loss': trade_metrics.avg_loss,
            'profit_factor': trade_metrics.profit_factor,
            'open_positions': len(self.get_open_positions()),
            'closed_positions': len(self.get_closed_positions())
        }

    def print_performance_summary(self) -> None:
        """Print formatted performance summary"""
        summary = self.get_portfolio_summary()

        print("\n" + "="*60)
        print("PORTFOLIO PERFORMANCE SUMMARY")
        print("="*60)
        print(f"Initial Capital:     ${summary['initial_capital']:,.2f}")
        print(f"Current Capital:     ${summary['current_capital']:,.2f}")
        print(f"Total Return:        ${summary['current_capital'] - summary['initial_capital']:,.2f} ({summary['total_return_pct']:.2f}%)")
        print(f"Peak Capital:        ${summary['peak_capital']:,.2f}")
        print(f"Max Drawdown:        {summary['max_drawdown_pct']:.2f}%")
        print()
        print("TRADE STATISTICS")
        print("-"*30)
        print(f"Total Trades:        {summary['total_trades']}")
        print(f"Winning Trades:      {summary['winning_trades']}")
        print(f"Losing Trades:       {summary['losing_trades']}")
        print(f"Win Rate:            {summary['win_rate_pct']:.2f}%")
        print(f"Average Win:         ${summary['avg_win']:.2f}")
        print(f"Average Loss:        ${summary['avg_loss']:.2f}")
        print(f"Profit Factor:       {summary['profit_factor']:.2f}")
        print()
        print("POSITION STATUS")
        print("-"*30)
        print(f"Open Positions:      {summary['open_positions']}")
        print(f"Closed Positions:    {summary['closed_positions']}")
        print("="*60)
