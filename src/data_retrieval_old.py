"""
Data Retrieval Module for SPX Overnight Options Strategy
Handles all Polygon.io API interactions for options and underlying data
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
from typing import Dict, List, Optional, Tuple, Union
import pytz
import logging
from dataclasses import dataclass

from config import StrategyConfiguration
from config_manager import load_config


@dataclass
class MarketDataPoint:
    """Represents a single market data point"""
    timestamp: datetime
    price: float
    volume: Optional[int] = None
    symbol: str = ""

    def is_valid(self, min_price: float = 0.01, max_price: float = 10000.0) -> bool:
        """Validate the data point"""
        return (
            self.price is not None and
            min_price <= self.price <= max_price and
            self.timestamp is not None
        )


@dataclass
class OptionsContract:
    """Represents an options contract with all relevant details"""
    ticker: str
    underlying_symbol: str
    contract_type: str  # 'call' or 'put'
    strike_price: float
    expiration_date: str
    shares_per_contract: int = 100
    last_price: Optional[float] = None
    bid: Optional[float] = None
    ask: Optional[float] = None
    volume: Optional[int] = None
    open_interest: Optional[int] = None
    implied_volatility: Optional[float] = None

    def is_valid(self) -> bool:
        """Validate the options contract data"""
        return (
            self.ticker and
            self.underlying_symbol and
            self.contract_type in ['call', 'put'] and
            self.strike_price > 0 and
            self.expiration_date and
            self.shares_per_contract > 0
        )


class DataRetrievalError(Exception):
    """Custom exception for data retrieval errors"""
    pass


class PolygonDataRetriever:
    """
    Handles data retrieval from Polygon.io API for SPX options strategy
    """

    def __init__(self, config: Optional[StrategyConfiguration] = None):
        """
        Initialize the data retriever with configuration

        Args:
            config: Strategy configuration. If None, loads from environment
        """
        self.config = config or load_config()
        self.api_config = self.config.api
        self.data_config = self.config.data

        # Set up session and rate limiting
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'SPX-Overnight-Strategy/1.0'
        })

        # Timezone setup
        self.et_tz = pytz.timezone(self.data_config.market_timezone)
        self.utc_tz = pytz.UTC

        # Set up logging
        self.logger = self._setup_logger()

        # Data validation parameters
        self.min_underlying_price = self.data_config.min_underlying_price
        self.max_underlying_price = self.data_config.max_underlying_price
        self.max_price_change = self.data_config.max_price_change

    def _setup_logger(self) -> logging.Logger:
        """Set up logging for data retrieval"""
        logger = logging.getLogger('data_retrieval')
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger
    
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """
        Make API request with rate limiting and error handling

        Args:
            endpoint: API endpoint path
            params: Query parameters

        Returns:
            JSON response data or None if failed

        Raises:
            DataRetrievalError: If request fails after retries
        """
        if params is None:
            params = {}

        params['apikey'] = self.api_config.polygon_api_key
        url = f"{self.api_config.base_url}{endpoint}"

        for attempt in range(self.api_config.max_retries):
            try:
                # Rate limiting
                time.sleep(self.api_config.rate_limit_delay)

                response = self.session.get(
                    url,
                    params=params,
                    timeout=self.api_config.timeout_seconds
                )
                response.raise_for_status()

                data = response.json()

                if data.get('status') == 'OK':
                    return data
                else:
                    error_msg = data.get('error', 'Unknown API error')
                    self.logger.warning(f"API Error (attempt {attempt + 1}): {error_msg}")
                    if attempt == self.api_config.max_retries - 1:
                        raise DataRetrievalError(f"API Error: {error_msg}")

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Request failed (attempt {attempt + 1}): {e}")
                if attempt == self.api_config.max_retries - 1:
                    raise DataRetrievalError(f"Request failed after {self.api_config.max_retries} attempts: {e}")

            except Exception as e:
                self.logger.error(f"Unexpected error: {e}")
                raise DataRetrievalError(f"Unexpected error: {e}")

        return None
    
    def get_spx_price(self, date: str) -> Optional[float]:
        """
        Get SPX index price for a specific date

        Args:
            date: Date in YYYY-MM-DD format

        Returns:
            SPX closing price or None if not available

        Raises:
            DataRetrievalError: If data retrieval fails
        """
        try:
            # Validate date format
            datetime.strptime(date, '%Y-%m-%d')

            # Get SPX daily bar for the date
            endpoint = f"/v2/aggs/ticker/I:{self.config.strategy.underlying_symbol}/range/1/day/{date}/{date}"
            data = self._make_request(endpoint)

            if data and data.get('results') and len(data['results']) > 0:
                price = data['results'][0]['c']  # closing price

                # Validate price
                if self._validate_underlying_price(price):
                    self.logger.debug(f"Retrieved {self.config.strategy.underlying_symbol} price for {date}: ${price:.2f}")
                    return price
                else:
                    self.logger.warning(f"Invalid {self.config.strategy.underlying_symbol} price for {date}: ${price:.2f}")
                    return None

            self.logger.warning(f"No {self.config.strategy.underlying_symbol} price data available for {date}")
            return None

        except ValueError as e:
            self.logger.error(f"Invalid date format: {date}")
            raise DataRetrievalError(f"Invalid date format: {date}")
        except DataRetrievalError:
            raise
        except Exception as e:
            self.logger.error(f"Error getting {self.config.strategy.underlying_symbol} price for {date}: {e}")
            raise DataRetrievalError(f"Failed to get {self.config.strategy.underlying_symbol} price: {e}")

    def _validate_underlying_price(self, price: float) -> bool:
        """
        Validate underlying asset price

        Args:
            price: Price to validate

        Returns:
            True if price is valid
        """
        return (
            price is not None and
            self.min_underlying_price <= price <= self.max_underlying_price
        )
    
    def get_options_chain(self, underlying: str = "I:SPX", 
                         expiration_date: str = None,
                         contract_type: str = "call") -> List[Dict]:
        """
        Get options chain for SPX
        
        Args:
            underlying: Underlying symbol (I:SPX for SPX index)
            expiration_date: Expiration date filter (YYYY-MM-DD)
            contract_type: "call" or "put"
            
        Returns:
            List of options contracts
        """
        try:
            # For historical backtesting, we'll use the contracts endpoint
            # to get available contracts and then construct typical option tickers
            endpoint = f"/v3/reference/options/contracts"
            params = {
                'underlying_ticker': 'SPX',  # Use SPX without I: prefix for reference
                'contract_type': contract_type,
                'limit': 250  # Maximum allowed
            }
            
            if expiration_date:
                params['expiration_date'] = expiration_date
            
            data = self._make_request(endpoint, params)
            
            # Convert contract data to chain format
            contracts = data.get('results', [])
            chain_data = []
            
            for contract in contracts:
                # Create a mock chain entry similar to snapshot format
                chain_entry = {
                    'details': {
                        'ticker': contract.get('ticker'),
                        'contract_type': contract.get('contract_type'),
                        'strike_price': contract.get('strike_price'),
                        'expiration_date': contract.get('expiration_date'),
                        'shares_per_contract': contract.get('shares_per_contract', 100)
                    }
                }
                chain_data.append(chain_entry)
            
            return chain_data
            
        except Exception as e:
            print(f"Error getting options chain: {e}")
            return []
    
    def find_atm_option(self, underlying_price: float, options_chain: List[Dict]) -> Optional[Dict]:
        """
        Find the at-the-money (ATM) call option from the chain
        
        Args:
            underlying_price: Current price of underlying (SPX)
            options_chain: List of options contracts
            
        Returns:
            ATM option contract or None
        """
        if not options_chain:
            return None
        
        # Find the strike closest to the underlying price
        min_diff = float('inf')
        atm_option = None
        
        for option in options_chain:
            if option.get('details', {}).get('contract_type') == 'call':
                strike = option.get('details', {}).get('strike_price')
                if strike:
                    diff = abs(strike - underlying_price)
                    if diff < min_diff:
                        min_diff = diff
                        atm_option = option
        
        return atm_option
    
    def get_option_price_at_time(self, option_ticker: str, date: str, 
                                time_str: str) -> Optional[float]:
        """
        Get option price at a specific time on a specific date
        
        Args:
            option_ticker: Options ticker symbol (e.g., O:SPX240315C05000000)
            date: Date in YYYY-MM-DD format
            time_str: Time in HH:MM format (ET)
            
        Returns:
            Option price or None if not available
        """
        try:
            # For backtesting, we'll use daily aggregates as a proxy
            # In a real implementation, you'd want minute-level data
            endpoint = f"/v2/aggs/ticker/{option_ticker}/range/1/day/{date}/{date}"
            data = self._make_request(endpoint)
            
            if data.get('results') and len(data['results']) > 0:
                # Use closing price as proxy for the time-specific price
                # This is a simplification - real implementation would need minute data
                return data['results'][0]['c']
            
            return None
            
        except Exception as e:
            print(f"Error getting option price for {option_ticker} on {date}: {e}")
            return None
    
    def get_option_historical_data(self, option_ticker: str, 
                                  start_date: str, end_date: str) -> pd.DataFrame:
        """
        Get historical OHLCV data for an option
        
        Args:
            option_ticker: Options ticker symbol
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            DataFrame with historical data
        """
        try:
            endpoint = f"/v2/aggs/ticker/{option_ticker}/range/1/day/{start_date}/{end_date}"
            data = self._make_request(endpoint)
            
            if data.get('results'):
                df = pd.DataFrame(data['results'])
                df['date'] = pd.to_datetime(df['t'], unit='ms')
                df = df.rename(columns={
                    'o': 'open',
                    'h': 'high', 
                    'l': 'low',
                    'c': 'close',
                    'v': 'volume'
                })
                df = df.set_index('date')
                return df[['open', 'high', 'low', 'close', 'volume']]
            
            return pd.DataFrame()
            
        except Exception as e:
            print(f"Error getting historical data for {option_ticker}: {e}")
            return pd.DataFrame()
    
    def get_next_expiration_date(self, current_date: str, 
                                target_days: int = 60) -> Optional[str]:
        """
        Get the next options expiration date targeting ~60 days out
        
        Args:
            current_date: Current date (YYYY-MM-DD)
            target_days: Target days to expiration (default 60)
            
        Returns:
            Next expiration date or None
        """
        try:
            current = datetime.strptime(current_date, '%Y-%m-%d')
            
            # Target date is approximately 60 days out
            target_date = current + timedelta(days=target_days)
            
            # Find the Friday closest to the target date
            # SPX options typically expire on Fridays
            days_to_friday = (4 - target_date.weekday()) % 7
            if days_to_friday == 0 and target_date.weekday() != 4:
                days_to_friday = 7
            
            expiration_date = target_date + timedelta(days=days_to_friday)
            
            # Ensure it's at least 55 days out and not more than 65 days
            days_out = (expiration_date - current).days
            if days_out < 55:
                expiration_date += timedelta(days=7)  # Next Friday
            elif days_out > 65:
                expiration_date -= timedelta(days=7)  # Previous Friday
            
            return expiration_date.strftime('%Y-%m-%d')
            
        except Exception as e:
            print(f"Error finding next expiration date: {e}")
            return None
    
    def get_vix_price(self, date: str) -> Optional[float]:
        """
        Get VIX closing price for a specific date
        
        Args:
            date: Date in YYYY-MM-DD format
            
        Returns:
            VIX closing price or None if not available
        """
        try:
            # VIX symbol in Polygon is I:VIX
            endpoint = f"/v1/open-close/I:VIX/{date}"
            params = {"adjusted": "true"}
            
            data = self._make_request(endpoint, params)
            
            if data and data.get('status') == 'OK':
                return data.get('close')
            
            return None
            
        except Exception as e:
            print(f"Error getting VIX price for {date}: {e}")
            return None
    
    def get_vix_moving_average(self, date: str, window: int = 20) -> Optional[float]:
        """
        Get VIX moving average for volatility regime detection
        
        Args:
            date: Current date in YYYY-MM-DD format
            window: Number of days for moving average
            
        Returns:
            VIX moving average or None if not available
        """
        try:
            # Get historical VIX data
            end_date = datetime.strptime(date, '%Y-%m-%d')
            start_date = end_date - timedelta(days=window + 10)  # Extra days for weekends
            
            endpoint = f"/v2/aggs/ticker/I:VIX/range/1/day/{start_date.strftime('%Y-%m-%d')}/{end_date.strftime('%Y-%m-%d')}"
            params = {"adjusted": "true", "sort": "asc", "limit": 50}
            
            data = self._make_request(endpoint, params)
            
            if data and data.get('results'):
                closes = [bar['c'] for bar in data['results'][-window:]]
                if len(closes) >= window:
                    return sum(closes) / len(closes)
            
            return None
            
        except Exception as e:
            print(f"Error getting VIX moving average for {date}: {e}")
            return None
    
    def validate_trading_day(self, date: str) -> bool:
        """
        Check if a date is a valid trading day (weekday, not holiday)
        
        Args:
            date: Date in YYYY-MM-DD format
            
        Returns:
            True if valid trading day, False otherwise
        """
        try:
            dt = datetime.strptime(date, '%Y-%m-%d')
            
            # Check if it's a weekday (Monday=0, Sunday=6)
            if dt.weekday() >= 5:  # Saturday or Sunday
                return False
            
            # Basic holiday check (can be expanded)
            # New Year's Day
            if dt.month == 1 and dt.day == 1:
                return False
            
            # Christmas
            if dt.month == 12 and dt.day == 25:
                return False
            
            # Independence Day
            if dt.month == 7 and dt.day == 4:
                return False
            
            return True
            
        except Exception as e:
            print(f"Error validating trading day {date}: {e}")
            return False

