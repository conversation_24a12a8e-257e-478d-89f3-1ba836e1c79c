"""
Refactored Option Pricing Module for SPX Overnight Strategy
Provides Black-Scholes pricing for historical backtesting when market data is unavailable
"""

import numpy as np
from scipy.stats import norm
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, NamedTuple, List
import math
import logging

from config import StrategyConfiguration
from config_manager import load_config


class OptionPricingError(Exception):
    """Custom exception for option pricing errors"""
    pass


class OptionParameters(NamedTuple):
    """Container for option pricing parameters"""
    underlying_price: float
    strike_price: float
    time_to_expiration: float
    risk_free_rate: float
    volatility: float
    option_type: str


class OptionPriceResult(NamedTuple):
    """Container for option pricing results"""
    price: float
    delta: Optional[float] = None
    gamma: Optional[float] = None
    theta: Optional[float] = None
    vega: Optional[float] = None
    rho: Optional[float] = None


class BlackScholesCalculator:
    """
    Black-Scholes option pricing calculator with configurable parameters
    """
    
    def __init__(self, config: Optional[StrategyConfiguration] = None):
        """
        Initialize Black-Scholes calculator
        
        Args:
            config: Strategy configuration. If None, loads from environment
        """
        self.config = config or load_config()
        self.pricing_config = self.config.pricing
        
        # Set up logging
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """Set up logging for option pricing"""
        logger = logging.getLogger('option_pricing')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def calculate_time_to_expiration(self, current_date: str, expiration_date: str) -> float:
        """
        Calculate time to expiration in years
        
        Args:
            current_date: Current date (YYYY-MM-DD)
            expiration_date: Expiration date (YYYY-MM-DD)
            
        Returns:
            Time to expiration in years
            
        Raises:
            OptionPricingError: If date calculation fails
        """
        try:
            current_dt = datetime.strptime(current_date, '%Y-%m-%d')
            expiration_dt = datetime.strptime(expiration_date, '%Y-%m-%d')
            
            days_to_expiration = (expiration_dt - current_dt).days
            
            if days_to_expiration < 0:
                raise OptionPricingError(f"Expiration date {expiration_date} is before current date {current_date}")
            
            # Convert to years (assuming 365 days per year)
            time_to_expiration = max(days_to_expiration / 365.0, 1/365.0)  # Minimum 1 day
            
            self.logger.debug(f"Time to expiration: {days_to_expiration} days ({time_to_expiration:.4f} years)")
            return time_to_expiration
            
        except ValueError as e:
            self.logger.error(f"Invalid date format: {e}")
            raise OptionPricingError(f"Invalid date format: {e}")
        except Exception as e:
            self.logger.error(f"Error calculating time to expiration: {e}")
            raise OptionPricingError(f"Time calculation failed: {e}")
    
    def _validate_inputs(self, S: float, K: float, T: float, r: float, sigma: float) -> None:
        """
        Validate Black-Scholes inputs
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (years)
            r: Risk-free rate
            sigma: Volatility
            
        Raises:
            OptionPricingError: If inputs are invalid
        """
        if S <= 0:
            raise OptionPricingError(f"Invalid underlying price: {S}")
        if K <= 0:
            raise OptionPricingError(f"Invalid strike price: {K}")
        if T <= 0:
            raise OptionPricingError(f"Invalid time to expiration: {T}")
        if sigma <= 0:
            raise OptionPricingError(f"Invalid volatility: {sigma}")
        if r < 0:
            raise OptionPricingError(f"Invalid risk-free rate: {r}")
    
    def black_scholes_call(self, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """
        Calculate Black-Scholes call option price
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (years)
            r: Risk-free rate
            sigma: Volatility
            
        Returns:
            Call option price
            
        Raises:
            OptionPricingError: If calculation fails
        """
        try:
            self._validate_inputs(S, K, T, r, sigma)
            
            d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            call_price = S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
            
            # Ensure non-negative price
            call_price = max(call_price, 0.0)
            
            # Validate result
            if not (self.pricing_config.min_option_price <= call_price <= self.pricing_config.max_option_price):
                self.logger.warning(f"Call price {call_price:.2f} outside valid range")
            
            return call_price
            
        except OptionPricingError:
            raise
        except Exception as e:
            self.logger.error(f"Error in Black-Scholes call calculation: {e}")
            raise OptionPricingError(f"Black-Scholes call calculation failed: {e}")
    
    def black_scholes_put(self, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """
        Calculate Black-Scholes put option price
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (years)
            r: Risk-free rate
            sigma: Volatility
            
        Returns:
            Put option price
            
        Raises:
            OptionPricingError: If calculation fails
        """
        try:
            self._validate_inputs(S, K, T, r, sigma)
            
            d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            put_price = K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
            
            # Ensure non-negative price
            put_price = max(put_price, 0.0)
            
            # Validate result
            if not (self.pricing_config.min_option_price <= put_price <= self.pricing_config.max_option_price):
                self.logger.warning(f"Put price {put_price:.2f} outside valid range")
            
            return put_price
            
        except OptionPricingError:
            raise
        except Exception as e:
            self.logger.error(f"Error in Black-Scholes put calculation: {e}")
            raise OptionPricingError(f"Black-Scholes put calculation failed: {e}")
    
    def calculate_option_price(self, underlying_price: float, strike_price: float,
                             current_date: str, expiration_date: str,
                             option_type: str = "call", 
                             volatility: Optional[float] = None,
                             risk_free_rate: Optional[float] = None) -> float:
        """
        Calculate option price using Black-Scholes
        
        Args:
            underlying_price: Current price of underlying
            strike_price: Strike price of option
            current_date: Current date (YYYY-MM-DD)
            expiration_date: Expiration date (YYYY-MM-DD)
            option_type: "call" or "put"
            volatility: Implied volatility (if None, uses default)
            risk_free_rate: Risk-free rate (if None, uses default)
            
        Returns:
            Option price
            
        Raises:
            OptionPricingError: If calculation fails
        """
        try:
            T = self.calculate_time_to_expiration(current_date, expiration_date)
            sigma = volatility or self.pricing_config.default_volatility
            r = risk_free_rate or self.pricing_config.risk_free_rate
            
            if option_type.lower() == "call":
                price = self.black_scholes_call(underlying_price, strike_price, T, r, sigma)
            elif option_type.lower() == "put":
                price = self.black_scholes_put(underlying_price, strike_price, T, r, sigma)
            else:
                raise OptionPricingError(f"Invalid option type: {option_type}")
            
            self.logger.debug(f"Calculated {option_type} price: ${price:.2f} "
                            f"(S=${underlying_price:.2f}, K=${strike_price:.2f}, T={T:.4f})")
            
            return price
                
        except OptionPricingError:
            raise
        except Exception as e:
            self.logger.error(f"Error calculating option price: {e}")
            raise OptionPricingError(f"Option price calculation failed: {e}")
    
    def calculate_greeks(self, S: float, K: float, T: float, r: float, sigma: float, 
                        option_type: str = "call") -> Dict[str, float]:
        """
        Calculate option Greeks
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (years)
            r: Risk-free rate
            sigma: Volatility
            option_type: "call" or "put"
            
        Returns:
            Dictionary with Greek values
            
        Raises:
            OptionPricingError: If calculation fails
        """
        try:
            self._validate_inputs(S, K, T, r, sigma)
            
            d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            # Delta
            if option_type.lower() == "call":
                delta = norm.cdf(d1)
            else:
                delta = norm.cdf(d1) - 1
            
            # Gamma (same for calls and puts)
            gamma = norm.pdf(d1) / (S * sigma * np.sqrt(T))
            
            # Theta
            if option_type.lower() == "call":
                theta = (-(S * norm.pdf(d1) * sigma) / (2 * np.sqrt(T)) 
                        - r * K * np.exp(-r * T) * norm.cdf(d2)) / 365
            else:
                theta = (-(S * norm.pdf(d1) * sigma) / (2 * np.sqrt(T)) 
                        + r * K * np.exp(-r * T) * norm.cdf(-d2)) / 365
            
            # Vega (same for calls and puts)
            vega = S * norm.pdf(d1) * np.sqrt(T) / 100
            
            # Rho
            if option_type.lower() == "call":
                rho = K * T * np.exp(-r * T) * norm.cdf(d2) / 100
            else:
                rho = -K * T * np.exp(-r * T) * norm.cdf(-d2) / 100
            
            return {
                'delta': delta,
                'gamma': gamma,
                'theta': theta,
                'vega': vega,
                'rho': rho
            }
            
        except OptionPricingError:
            raise
        except Exception as e:
            self.logger.error(f"Error calculating Greeks: {e}")
            raise OptionPricingError(f"Greeks calculation failed: {e}")


class SyntheticOptionsGenerator:
    """
    Generates synthetic options data for backtesting when market data is unavailable
    """

    def __init__(self, config: Optional[StrategyConfiguration] = None):
        """
        Initialize synthetic options generator

        Args:
            config: Strategy configuration. If None, loads from environment
        """
        self.config = config or load_config()
        self.pricing_config = self.config.pricing
        self.bs_calculator = BlackScholesCalculator(config)

        # Set up logging
        self.logger = self._setup_logger()

    def _setup_logger(self) -> logging.Logger:
        """Set up logging for synthetic options generator"""
        logger = logging.getLogger('synthetic_options')
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _round_to_strike_increment(self, price: float) -> float:
        """
        Round price to nearest strike increment

        Args:
            price: Price to round

        Returns:
            Rounded price
        """
        increment = self.pricing_config.strike_increment
        return round(price / increment) * increment

    def generate_atm_option(self, underlying_price: float, current_date: str,
                           expiration_date: str, option_type: str = "call") -> Optional[Dict]:
        """
        Generate synthetic ATM option

        Args:
            underlying_price: Current price of underlying
            current_date: Current date (YYYY-MM-DD)
            expiration_date: Expiration date (YYYY-MM-DD)
            option_type: "call" or "put"

        Returns:
            Synthetic option data dictionary or None if generation fails

        Raises:
            OptionPricingError: If option generation fails
        """
        try:
            # Validate inputs
            if underlying_price <= 0:
                raise OptionPricingError(f"Invalid underlying price: {underlying_price}")

            if option_type.lower() not in ['call', 'put']:
                raise OptionPricingError(f"Invalid option type: {option_type}")

            # Calculate ATM strike (rounded to increment)
            atm_strike = self._round_to_strike_increment(underlying_price)

            # Calculate option price using Black-Scholes
            option_price = self.bs_calculator.calculate_option_price(
                underlying_price=underlying_price,
                strike_price=atm_strike,
                current_date=current_date,
                expiration_date=expiration_date,
                option_type=option_type
            )

            # Generate synthetic ticker (simplified format)
            ticker = self._generate_option_ticker(
                underlying_symbol=self.config.strategy.underlying_symbol,
                expiration_date=expiration_date,
                option_type=option_type,
                strike_price=atm_strike
            )

            # Create synthetic option data
            option_data = {
                'details': {
                    'ticker': ticker,
                    'contract_type': option_type.lower(),
                    'strike_price': atm_strike,
                    'expiration_date': expiration_date,
                    'shares_per_contract': self.config.risk_management.contract_multiplier
                },
                'synthetic_price': option_price,
                'underlying_price': underlying_price,
                'time_to_expiration': self.bs_calculator.calculate_time_to_expiration(
                    current_date, expiration_date
                )
            }

            self.logger.info(f"Generated synthetic {option_type} option: {ticker} "
                           f"(strike: ${atm_strike:.2f}, price: ${option_price:.2f})")

            return option_data

        except OptionPricingError:
            raise
        except Exception as e:
            self.logger.error(f"Error generating synthetic option: {e}")
            raise OptionPricingError(f"Synthetic option generation failed: {e}")

    def _generate_option_ticker(self, underlying_symbol: str, expiration_date: str,
                               option_type: str, strike_price: float) -> str:
        """
        Generate option ticker symbol

        Args:
            underlying_symbol: Underlying symbol (e.g., SPX)
            expiration_date: Expiration date (YYYY-MM-DD)
            option_type: "call" or "put"
            strike_price: Strike price

        Returns:
            Option ticker symbol
        """
        try:
            exp_dt = datetime.strptime(expiration_date, '%Y-%m-%d')
            exp_str = exp_dt.strftime('%y%m%d')
            type_code = 'C' if option_type.lower() == 'call' else 'P'
            strike_str = f"{int(strike_price * 1000):08d}"

            ticker = f"O:{underlying_symbol}{exp_str}{type_code}{strike_str}"
            return ticker

        except Exception as e:
            self.logger.error(f"Error generating option ticker: {e}")
            return f"O:{underlying_symbol}_SYNTHETIC"

    def generate_options_chain(self, underlying_price: float, current_date: str,
                              expiration_date: str, option_type: str = "call",
                              num_strikes: int = 10) -> List[Dict]:
        """
        Generate synthetic options chain

        Args:
            underlying_price: Current price of underlying
            current_date: Current date (YYYY-MM-DD)
            expiration_date: Expiration date (YYYY-MM-DD)
            option_type: "call" or "put"
            num_strikes: Number of strikes to generate around ATM

        Returns:
            List of synthetic option data dictionaries

        Raises:
            OptionPricingError: If chain generation fails
        """
        try:
            options_chain = []

            # Calculate strike range
            strike_range = underlying_price * self.pricing_config.strike_range
            increment = self.pricing_config.strike_increment

            # Generate strikes around ATM
            atm_strike = self._round_to_strike_increment(underlying_price)

            for i in range(-num_strikes//2, num_strikes//2 + 1):
                strike = atm_strike + (i * increment)

                if strike <= 0:
                    continue

                try:
                    option_price = self.bs_calculator.calculate_option_price(
                        underlying_price=underlying_price,
                        strike_price=strike,
                        current_date=current_date,
                        expiration_date=expiration_date,
                        option_type=option_type
                    )

                    ticker = self._generate_option_ticker(
                        underlying_symbol=self.config.strategy.underlying_symbol,
                        expiration_date=expiration_date,
                        option_type=option_type,
                        strike_price=strike
                    )

                    option_data = {
                        'details': {
                            'ticker': ticker,
                            'contract_type': option_type.lower(),
                            'strike_price': strike,
                            'expiration_date': expiration_date,
                            'shares_per_contract': self.config.risk_management.contract_multiplier
                        },
                        'synthetic_price': option_price,
                        'underlying_price': underlying_price
                    }

                    options_chain.append(option_data)

                except Exception as e:
                    self.logger.warning(f"Failed to generate option for strike {strike}: {e}")
                    continue

            self.logger.info(f"Generated synthetic options chain with {len(options_chain)} strikes")
            return options_chain

        except Exception as e:
            self.logger.error(f"Error generating options chain: {e}")
            raise OptionPricingError(f"Options chain generation failed: {e}")
