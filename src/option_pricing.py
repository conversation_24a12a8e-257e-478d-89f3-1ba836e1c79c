"""
Option Pricing Module for SPX Overnight Strategy
Provides Black-Scholes pricing for historical backtesting when market data is unavailable
"""

import numpy as np
from scipy.stats import norm
from datetime import datetime, timedelta
from typing import Optional
import math

class BlackScholesCalculator:
    """
    Black-Scholes option pricing calculator
    """
    
    def __init__(self):
        self.risk_free_rate = 0.05  # 5% risk-free rate assumption
        self.default_volatility = 0.20  # 20% default volatility
    
    def calculate_time_to_expiration(self, current_date: str, expiration_date: str) -> float:
        """
        Calculate time to expiration in years
        
        Args:
            current_date: Current date (YYYY-MM-DD)
            expiration_date: Expiration date (YYYY-MM-DD)
            
        Returns:
            Time to expiration in years
        """
        try:
            current_dt = datetime.strptime(current_date, '%Y-%m-%d')
            exp_dt = datetime.strptime(expiration_date, '%Y-%m-%d')
            
            days_to_exp = (exp_dt - current_dt).days
            return max(days_to_exp / 365.0, 0.001)  # Minimum 0.001 years
            
        except Exception as e:
            print(f"Error calculating time to expiration: {e}")
            return 0.001
    
    def black_scholes_call(self, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """
        Calculate Black-Scholes call option price
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (years)
            r: Risk-free rate
            sigma: Volatility
            
        Returns:
            Call option price
        """
        try:
            if T <= 0 or sigma <= 0 or S <= 0 or K <= 0:
                return 0.0
            
            d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            call_price = S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
            
            return max(call_price, 0.0)
            
        except Exception as e:
            print(f"Error in Black-Scholes calculation: {e}")
            return 0.0
    
    def black_scholes_put(self, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """
        Calculate Black-Scholes put option price
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (years)
            r: Risk-free rate
            sigma: Volatility
            
        Returns:
            Put option price
        """
        try:
            if T <= 0 or sigma <= 0 or S <= 0 or K <= 0:
                return 0.0
            
            d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            put_price = K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
            
            return max(put_price, 0.0)
            
        except Exception as e:
            print(f"Error in Black-Scholes calculation: {e}")
            return 0.0
    
    def calculate_option_price(self, underlying_price: float, strike_price: float,
                             current_date: str, expiration_date: str,
                             option_type: str = "call", volatility: float = None) -> float:
        """
        Calculate option price using Black-Scholes
        
        Args:
            underlying_price: Current price of underlying
            strike_price: Strike price of option
            current_date: Current date (YYYY-MM-DD)
            expiration_date: Expiration date (YYYY-MM-DD)
            option_type: "call" or "put"
            volatility: Implied volatility (if None, uses default)
            
        Returns:
            Option price
        """
        try:
            T = self.calculate_time_to_expiration(current_date, expiration_date)
            sigma = volatility or self.default_volatility
            
            if option_type.lower() == "call":
                return self.black_scholes_call(
                    underlying_price, strike_price, T, self.risk_free_rate, sigma
                )
            else:
                return self.black_scholes_put(
                    underlying_price, strike_price, T, self.risk_free_rate, sigma
                )
                
        except Exception as e:
            print(f"Error calculating option price: {e}")
            return 0.0
    
    def estimate_volatility_from_price_history(self, prices: list, window: int = 20) -> float:
        """
        Estimate volatility from historical price data
        
        Args:
            prices: List of historical prices
            window: Number of periods for calculation
            
        Returns:
            Annualized volatility
        """
        try:
            if len(prices) < 2:
                return self.default_volatility
            
            # Calculate daily returns
            returns = []
            for i in range(1, min(len(prices), window + 1)):
                if prices[i-1] > 0:
                    returns.append(np.log(prices[i] / prices[i-1]))
            
            if len(returns) < 2:
                return self.default_volatility
            
            # Calculate standard deviation and annualize
            daily_vol = np.std(returns)
            annual_vol = daily_vol * np.sqrt(252)  # 252 trading days per year
            
            return max(annual_vol, 0.05)  # Minimum 5% volatility
            
        except Exception as e:
            print(f"Error estimating volatility: {e}")
            return self.default_volatility

class SyntheticOptionsGenerator:
    """
    Generates synthetic options data for backtesting when market data is unavailable
    """
    
    def __init__(self):
        self.bs_calculator = BlackScholesCalculator()
        self.strike_range = 0.10  # Generate strikes within 10% of underlying price
        self.strike_increment = 25  # $25 strike increments for SPX
    
    def generate_atm_option(self, underlying_price: float, current_date: str,
                           expiration_date: str, option_type: str = "call") -> dict:
        """
        Generate synthetic ATM option
        
        Args:
            underlying_price: Current price of underlying
            current_date: Current date (YYYY-MM-DD)
            expiration_date: Expiration date (YYYY-MM-DD)
            option_type: "call" or "put"
            
        Returns:
            Synthetic option data dictionary
        """
        try:
            # Find nearest strike price (round to nearest strike increment)
            atm_strike = round(underlying_price / self.strike_increment) * self.strike_increment
            
            # Calculate option price using Black-Scholes
            option_price = self.bs_calculator.calculate_option_price(
                underlying_price, atm_strike, current_date, expiration_date, option_type
            )
            
            # Generate synthetic ticker (simplified format)
            exp_dt = datetime.strptime(expiration_date, '%Y-%m-%d')
            exp_str = exp_dt.strftime('%y%m%d')
            type_code = 'C' if option_type.lower() == 'call' else 'P'
            strike_str = f"{int(atm_strike * 1000):08d}"
            
            ticker = f"O:SPX{exp_str}{type_code}{strike_str}"
            
            # Create synthetic option data
            option_data = {
                'details': {
                    'ticker': ticker,
                    'contract_type': option_type.lower(),
                    'strike_price': atm_strike,
                    'expiration_date': expiration_date,
                    'shares_per_contract': 100
                },
                'synthetic_price': option_price,
                'underlying_price': underlying_price,
                'time_to_expiration': self.bs_calculator.calculate_time_to_expiration(
                    current_date, expiration_date
                )
            }
            
            return option_data
            
        except Exception as e:
            print(f"Error generating synthetic option: {e}")
            return {}
    
    def generate_options_chain(self, underlying_price: float, current_date: str,
                             expiration_date: str, option_type: str = "call") -> list:
        """
        Generate synthetic options chain
        
        Args:
            underlying_price: Current price of underlying
            current_date: Current date (YYYY-MM-DD)
            expiration_date: Expiration date (YYYY-MM-DD)
            option_type: "call" or "put"
            
        Returns:
            List of synthetic options
        """
        try:
            options_chain = []
            
            # Generate strikes around the underlying price
            min_strike = underlying_price * (1 - self.strike_range)
            max_strike = underlying_price * (1 + self.strike_range)
            
            current_strike = (int(min_strike / self.strike_increment) * 
                            self.strike_increment)
            
            while current_strike <= max_strike:
                option_price = self.bs_calculator.calculate_option_price(
                    underlying_price, current_strike, current_date, 
                    expiration_date, option_type
                )
                
                if option_price > 0.01:  # Only include options with meaningful value
                    # Generate ticker
                    exp_dt = datetime.strptime(expiration_date, '%Y-%m-%d')
                    exp_str = exp_dt.strftime('%y%m%d')
                    type_code = 'C' if option_type.lower() == 'call' else 'P'
                    strike_str = f"{int(current_strike * 1000):08d}"
                    
                    ticker = f"O:SPX{exp_str}{type_code}{strike_str}"
                    
                    option_data = {
                        'details': {
                            'ticker': ticker,
                            'contract_type': option_type.lower(),
                            'strike_price': current_strike,
                            'expiration_date': expiration_date,
                            'shares_per_contract': 100
                        },
                        'synthetic_price': option_price,
                        'underlying_price': underlying_price
                    }
                    
                    options_chain.append(option_data)
                
                current_strike += self.strike_increment
            
            return options_chain
            
        except Exception as e:
            print(f"Error generating options chain: {e}")
            return []

