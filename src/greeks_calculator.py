#!/usr/bin/env python3
"""
Greeks Calculator for SPX Options using real Polygon data
Calculates Delta, Gamma, Theta, Vega from real market prices
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import logging
from scipy.stats import norm
from scipy.optimize import minimize_scalar

from src.config import StrategyConfiguration as Config
from src.data_retrieval import PolygonDataRetriever


class GreeksCalculator:
    """Calculate option Greeks from real market data"""
    
    def __init__(self, config: Config):
        self.config = config
        self.data_retriever = PolygonDataRetriever(config)
        self.logger = logging.getLogger(__name__)
        
    def calculate_implied_volatility(self, option_price: float, underlying_price: float, 
                                   strike_price: float, time_to_expiration: float, 
                                   risk_free_rate: float = 0.05, option_type: str = 'call') -> Optional[float]:
        """
        Calculate implied volatility from market option price using Newton-<PERSON><PERSON>on method
        
        Args:
            option_price: Market price of the option
            underlying_price: Current price of underlying asset
            strike_price: Strike price of the option
            time_to_expiration: Time to expiration in years
            risk_free_rate: Risk-free interest rate
            option_type: 'call' or 'put'
            
        Returns:
            Implied volatility or None if calculation fails
        """
        try:
            def black_scholes_price(vol):
                d1 = (np.log(underlying_price / strike_price) + (risk_free_rate + 0.5 * vol**2) * time_to_expiration) / (vol * np.sqrt(time_to_expiration))
                d2 = d1 - vol * np.sqrt(time_to_expiration)
                
                if option_type.lower() == 'call':
                    return underlying_price * norm.cdf(d1) - strike_price * np.exp(-risk_free_rate * time_to_expiration) * norm.cdf(d2)
                else:
                    return strike_price * np.exp(-risk_free_rate * time_to_expiration) * norm.cdf(-d2) - underlying_price * norm.cdf(-d1)
            
            def objective(vol):
                if vol <= 0:
                    return float('inf')
                try:
                    theoretical_price = black_scholes_price(vol)
                    return abs(theoretical_price - option_price)
                except:
                    return float('inf')
            
            # Use optimization to find implied volatility
            result = minimize_scalar(objective, bounds=(0.01, 5.0), method='bounded')
            
            if result.success and result.fun < 0.01:  # Good convergence
                return result.x
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Error calculating implied volatility: {e}")
            return None
    
    def calculate_greeks_from_market_data(self, option_ticker: str, underlying_price: float,
                                        strike_price: float, expiration_date: str, 
                                        current_date: str, option_type: str = 'call') -> Optional[Dict]:
        """
        Calculate Greeks using real market option price from Polygon
        
        Args:
            option_ticker: Options ticker symbol
            underlying_price: Current underlying price
            strike_price: Strike price
            expiration_date: Expiration date (YYYY-MM-DD)
            current_date: Current date (YYYY-MM-DD)
            option_type: 'call' or 'put'
            
        Returns:
            Dictionary with Greeks or None if calculation fails
        """
        try:
            # Get real market option price
            option_data = self.data_retriever.get_historical_option_price(option_ticker, current_date)
            if not option_data or not option_data.get('close'):
                self.logger.warning(f"No market data available for {option_ticker} on {current_date}")
                return None
            
            market_price = option_data['close']
            
            # Calculate time to expiration
            current_dt = datetime.strptime(current_date, '%Y-%m-%d')
            expiration_dt = datetime.strptime(expiration_date, '%Y-%m-%d')
            time_to_expiration = (expiration_dt - current_dt).days / 365.0
            
            if time_to_expiration <= 0:
                self.logger.warning(f"Option {option_ticker} has expired")
                return None
            
            # Calculate implied volatility from market price
            implied_vol = self.calculate_implied_volatility(
                market_price, underlying_price, strike_price, 
                time_to_expiration, option_type=option_type
            )
            
            if not implied_vol:
                self.logger.warning(f"Could not calculate implied volatility for {option_ticker}")
                return None
            
            # Calculate Greeks using Black-Scholes formulas
            greeks = self._calculate_black_scholes_greeks(
                underlying_price, strike_price, time_to_expiration, 
                implied_vol, option_type=option_type
            )
            
            greeks.update({
                'market_price': market_price,
                'implied_volatility': implied_vol,
                'time_to_expiration': time_to_expiration,
                'underlying_price': underlying_price,
                'strike_price': strike_price,
                'volume': option_data.get('volume', 0),
                'date': current_date
            })
            
            self.logger.info(f"✅ Calculated Greeks for {option_ticker}: Delta={greeks['delta']:.3f}, Gamma={greeks['gamma']:.3f}, Theta={greeks['theta']:.3f}, Vega={greeks['vega']:.3f}")
            
            return greeks
            
        except Exception as e:
            self.logger.error(f"Error calculating Greeks for {option_ticker}: {e}")
            return None
    
    def _calculate_black_scholes_greeks(self, S: float, K: float, T: float, 
                                      sigma: float, r: float = 0.05, 
                                      option_type: str = 'call') -> Dict:
        """
        Calculate Black-Scholes Greeks
        
        Args:
            S: Underlying price
            K: Strike price
            T: Time to expiration (years)
            sigma: Volatility
            r: Risk-free rate
            option_type: 'call' or 'put'
            
        Returns:
            Dictionary with Greeks
        """
        try:
            # Calculate d1 and d2
            d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            # Calculate Greeks
            if option_type.lower() == 'call':
                delta = norm.cdf(d1)
                theta = (-S * norm.pdf(d1) * sigma / (2 * np.sqrt(T)) 
                        - r * K * np.exp(-r * T) * norm.cdf(d2)) / 365
            else:  # put
                delta = norm.cdf(d1) - 1
                theta = (-S * norm.pdf(d1) * sigma / (2 * np.sqrt(T)) 
                        + r * K * np.exp(-r * T) * norm.cdf(-d2)) / 365
            
            gamma = norm.pdf(d1) / (S * sigma * np.sqrt(T))
            vega = S * norm.pdf(d1) * np.sqrt(T) / 100  # Per 1% change in volatility
            rho = K * T * np.exp(-r * T) * norm.cdf(d2 if option_type.lower() == 'call' else -d2) / 100
            
            return {
                'delta': delta,
                'gamma': gamma,
                'theta': theta,
                'vega': vega,
                'rho': rho
            }
            
        except Exception as e:
            self.logger.error(f"Error in Black-Scholes Greeks calculation: {e}")
            return {}
    
    def analyze_overnight_greeks_changes(self, option_ticker: str, underlying_symbol: str,
                                       strike_price: float, expiration_date: str,
                                       start_date: str, end_date: str) -> pd.DataFrame:
        """
        Analyze overnight changes in Greeks for a specific option
        
        Args:
            option_ticker: Options ticker symbol
            underlying_symbol: Underlying asset symbol (e.g., 'I:SPX')
            strike_price: Strike price
            expiration_date: Expiration date
            start_date: Analysis start date
            end_date: Analysis end date
            
        Returns:
            DataFrame with daily Greeks and overnight changes
        """
        try:
            # Get historical options data
            options_data = self.data_retriever.get_historical_options_data_range(
                option_ticker, start_date, end_date
            )
            
            if not options_data:
                self.logger.warning(f"No historical options data for {option_ticker}")
                return pd.DataFrame()
            
            # Get underlying price data
            underlying_data = {}
            current_date = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            
            while current_date <= end_dt:
                date_str = current_date.strftime('%Y-%m-%d')
                underlying_price = self.data_retriever.get_underlying_price(underlying_symbol, date_str)
                if underlying_price:
                    underlying_data[date_str] = underlying_price
                current_date += timedelta(days=1)
            
            # Calculate Greeks for each day
            greeks_data = []
            
            for option_day in options_data:
                date = option_day['date']
                if date in underlying_data:
                    greeks = self.calculate_greeks_from_market_data(
                        option_ticker, underlying_data[date], strike_price,
                        expiration_date, date, 'call'
                    )
                    
                    if greeks:
                        greeks_data.append(greeks)
            
            if not greeks_data:
                return pd.DataFrame()
            
            # Convert to DataFrame and calculate overnight changes
            df = pd.DataFrame(greeks_data)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # Calculate overnight changes
            for greek in ['delta', 'gamma', 'theta', 'vega']:
                df[f'{greek}_change'] = df[greek].diff()
                df[f'{greek}_pct_change'] = df[greek].pct_change() * 100
            
            df['price_change'] = df['market_price'].diff()
            df['price_pct_change'] = df['market_price'].pct_change() * 100
            
            self.logger.info(f"✅ Analyzed {len(df)} days of Greeks data for {option_ticker}")
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error analyzing overnight Greeks changes: {e}")
            return pd.DataFrame()
