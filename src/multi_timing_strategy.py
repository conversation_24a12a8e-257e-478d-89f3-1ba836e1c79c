#!/usr/bin/env python3
"""
Multi-Timing SPX Options Strategy Framework
Tests all combinations of entry/exit times with real Polygon data
"""

import os
import json
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

from src.config import StrategyConfiguration as Config
from src.overnight_strategy import SPXOvernightStrategy
from src.greeks_calculator import GreeksCalculator


@dataclass
class TimingCombination:
    """Represents a specific entry/exit timing combination"""
    entry_time: str
    exit_time: str
    name: str
    
    def __str__(self):
        return f"{self.name} (Entry: {self.entry_time}, Exit: {self.exit_time})"


@dataclass
class TimingResults:
    """Results for a specific timing combination"""
    timing: TimingCombination
    total_return: float
    total_trades: int
    win_rate: float
    avg_return_per_trade: float
    max_drawdown: float
    sharpe_ratio: float
    profit_factor: float
    trades_data: List[Dict]
    
    def to_dict(self) -> Dict:
        return {
            'timing_name': self.timing.name,
            'entry_time': self.timing.entry_time,
            'exit_time': self.timing.exit_time,
            'total_return': self.total_return,
            'total_trades': self.total_trades,
            'win_rate': self.win_rate,
            'avg_return_per_trade': self.avg_return_per_trade,
            'max_drawdown': self.max_drawdown,
            'sharpe_ratio': self.sharpe_ratio,
            'profit_factor': self.profit_factor
        }


class MultiTimingStrategy:
    """Framework for testing multiple entry/exit timing combinations"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.greeks_calculator = GreeksCalculator(config)
        
        # Define timing combinations to test
        self.timing_combinations = [
            TimingCombination("12:00", "10:00", "Noon-to-10AM"),
            TimingCombination("12:00", "11:00", "Noon-to-11AM"), 
            TimingCombination("12:00", "12:00", "Noon-to-Noon"),
            TimingCombination("13:00", "10:00", "1PM-to-10AM"),
            TimingCombination("13:00", "11:00", "1PM-to-11AM"),
            TimingCombination("13:00", "12:00", "1PM-to-Noon"),
            TimingCombination("14:00", "10:00", "2PM-to-10AM"),
            TimingCombination("14:00", "11:00", "2PM-to-11AM"),
            TimingCombination("14:00", "12:00", "2PM-to-Noon"),
            TimingCombination("15:00", "10:00", "3PM-to-10AM"),
            TimingCombination("15:00", "11:00", "3PM-to-11AM"),
            TimingCombination("15:00", "12:00", "3PM-to-Noon"),
            TimingCombination("16:00", "10:00", "4PM-to-10AM"),
            TimingCombination("16:00", "11:00", "4PM-to-11AM"),
            TimingCombination("16:00", "12:00", "4PM-to-Noon"),
        ]
        
    def run_comprehensive_analysis(self, start_date: str, end_date: str, 
                                 initial_capital: float = 100000) -> Dict:
        """
        Run comprehensive analysis of all timing combinations
        
        Args:
            start_date: Start date for backtesting (YYYY-MM-DD)
            end_date: End date for backtesting (YYYY-MM-DD)
            initial_capital: Initial capital for backtesting
            
        Returns:
            Dictionary with comprehensive results
        """
        try:
            self.logger.info(f"🚀 Starting comprehensive multi-timing analysis")
            self.logger.info(f"📅 Period: {start_date} to {end_date}")
            self.logger.info(f"💰 Initial Capital: ${initial_capital:,.2f}")
            self.logger.info(f"⏰ Testing {len(self.timing_combinations)} timing combinations")
            
            results = []
            
            for i, timing in enumerate(self.timing_combinations, 1):
                self.logger.info(f"\n{'='*60}")
                self.logger.info(f"🔄 Testing {i}/{len(self.timing_combinations)}: {timing}")
                self.logger.info(f"{'='*60}")
                
                # Run backtest for this timing combination
                timing_result = self._run_timing_backtest(
                    timing, start_date, end_date, initial_capital
                )
                
                if timing_result:
                    results.append(timing_result)
                    self.logger.info(f"✅ {timing.name}: Return={timing_result.total_return:.2f}%, Trades={timing_result.total_trades}, Win Rate={timing_result.win_rate:.1f}%")
                else:
                    self.logger.warning(f"❌ Failed to get results for {timing.name}")
            
            # Analyze and rank results
            analysis = self._analyze_timing_results(results)
            
            # Generate comprehensive report
            report_path = self._generate_timing_report(analysis, start_date, end_date)
            
            self.logger.info(f"\n🎉 Comprehensive analysis completed!")
            self.logger.info(f"📊 Report generated: {report_path}")
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive analysis: {e}")
            return {}
    
    def _run_timing_backtest(self, timing: TimingCombination, start_date: str, 
                           end_date: str, initial_capital: float) -> Optional[TimingResults]:
        """
        Run backtest for a specific timing combination
        
        Args:
            timing: Timing combination to test
            start_date: Start date
            end_date: End date
            initial_capital: Initial capital
            
        Returns:
            TimingResults or None if failed
        """
        try:
            # Create modified config for this timing
            timing_config = self._create_timing_config(timing)
            
            # Initialize strategy with timing-specific config
            strategy = SPXOvernightStrategy(timing_config)
            
            # Run backtest
            results = strategy.run_backtest(start_date, end_date, initial_capital)
            
            if not results:
                return None
            
            # Get performance summary
            performance = strategy.get_performance_summary()
            
            # Calculate additional metrics
            trades_data = strategy.position_manager.get_trade_history()
            
            if not trades_data:
                return TimingResults(
                    timing=timing,
                    total_return=0.0,
                    total_trades=0,
                    win_rate=0.0,
                    avg_return_per_trade=0.0,
                    max_drawdown=0.0,
                    sharpe_ratio=0.0,
                    profit_factor=0.0,
                    trades_data=[]
                )
            
            # Calculate metrics
            returns = [trade.get('return_percentage', 0) for trade in trades_data]
            winning_trades = [r for r in returns if r > 0]
            losing_trades = [r for r in returns if r < 0]
            
            win_rate = len(winning_trades) / len(returns) * 100 if returns else 0
            avg_return = sum(returns) / len(returns) if returns else 0
            
            # Calculate profit factor
            total_wins = sum(winning_trades) if winning_trades else 0
            total_losses = abs(sum(losing_trades)) if losing_trades else 1
            profit_factor = total_wins / total_losses if total_losses > 0 else 0
            
            # Calculate Sharpe ratio (simplified)
            if returns and len(returns) > 1:
                returns_std = pd.Series(returns).std()
                sharpe_ratio = (avg_return / returns_std) if returns_std > 0 else 0
            else:
                sharpe_ratio = 0
            
            return TimingResults(
                timing=timing,
                total_return=performance.get('total_return_pct', 0),
                total_trades=len(trades_data),
                win_rate=win_rate,
                avg_return_per_trade=avg_return,
                max_drawdown=performance.get('max_drawdown_pct', 0),
                sharpe_ratio=sharpe_ratio,
                profit_factor=profit_factor,
                trades_data=trades_data
            )
            
        except Exception as e:
            self.logger.error(f"Error running backtest for {timing.name}: {e}")
            return None
    
    def _create_timing_config(self, timing: TimingCombination) -> Config:
        """
        Create a modified config for specific timing combination
        
        Args:
            timing: Timing combination
            
        Returns:
            Modified config
        """
        # Create a copy of the current config
        timing_config = Config()
        
        # Update timing settings
        timing_config.strategy.entry_time = timing.entry_time
        timing_config.strategy.exit_time = timing.exit_time
        
        return timing_config
    
    def _analyze_timing_results(self, results: List[TimingResults]) -> Dict:
        """
        Analyze and rank timing results
        
        Args:
            results: List of timing results
            
        Returns:
            Analysis dictionary
        """
        try:
            if not results:
                return {}
            
            # Convert to DataFrame for analysis
            df = pd.DataFrame([r.to_dict() for r in results])
            
            # Rank by different metrics
            rankings = {
                'by_total_return': df.nlargest(5, 'total_return')[['timing_name', 'total_return', 'total_trades', 'win_rate']],
                'by_win_rate': df.nlargest(5, 'win_rate')[['timing_name', 'win_rate', 'total_return', 'total_trades']],
                'by_sharpe_ratio': df.nlargest(5, 'sharpe_ratio')[['timing_name', 'sharpe_ratio', 'total_return', 'win_rate']],
                'by_profit_factor': df.nlargest(5, 'profit_factor')[['timing_name', 'profit_factor', 'total_return', 'win_rate']]
            }
            
            # Calculate summary statistics
            summary_stats = {
                'total_combinations_tested': len(results),
                'profitable_combinations': len(df[df['total_return'] > 0]),
                'avg_return_all_combinations': df['total_return'].mean(),
                'best_combination': df.loc[df['total_return'].idxmax()].to_dict() if len(df) > 0 else {},
                'worst_combination': df.loc[df['total_return'].idxmin()].to_dict() if len(df) > 0 else {},
                'avg_win_rate': df['win_rate'].mean(),
                'avg_trades_per_combination': df['total_trades'].mean()
            }
            
            return {
                'summary_stats': summary_stats,
                'rankings': rankings,
                'all_results': df,
                'detailed_results': results
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing timing results: {e}")
            return {}
    
    def _generate_timing_report(self, analysis: Dict, start_date: str, end_date: str) -> str:
        """
        Generate comprehensive timing analysis report
        
        Args:
            analysis: Analysis results
            start_date: Start date
            end_date: End date
            
        Returns:
            Path to generated report
        """
        try:
            report_dir = "report/timing_analysis"
            os.makedirs(report_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_path = os.path.join(report_dir, f"multi_timing_analysis_{timestamp}.md")
            
            with open(report_path, 'w') as f:
                f.write(f"# SPX Options Multi-Timing Strategy Analysis\n\n")
                f.write(f"**Analysis Period**: {start_date} to {end_date}\n")
                f.write(f"**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # Summary statistics
                if 'summary_stats' in analysis:
                    stats = analysis['summary_stats']
                    f.write(f"## Summary Statistics\n\n")
                    f.write(f"- **Total Combinations Tested**: {stats.get('total_combinations_tested', 0)}\n")
                    f.write(f"- **Profitable Combinations**: {stats.get('profitable_combinations', 0)}\n")
                    f.write(f"- **Average Return**: {stats.get('avg_return_all_combinations', 0):.2f}%\n")
                    f.write(f"- **Average Win Rate**: {stats.get('avg_win_rate', 0):.1f}%\n")
                    f.write(f"- **Average Trades per Combination**: {stats.get('avg_trades_per_combination', 0):.1f}\n\n")
                
                # Best and worst combinations
                if 'summary_stats' in analysis:
                    best = analysis['summary_stats'].get('best_combination', {})
                    worst = analysis['summary_stats'].get('worst_combination', {})
                    
                    f.write(f"## Best vs Worst Performance\n\n")
                    f.write(f"### 🏆 Best Combination: {best.get('timing_name', 'N/A')}\n")
                    f.write(f"- **Return**: {best.get('total_return', 0):.2f}%\n")
                    f.write(f"- **Win Rate**: {best.get('win_rate', 0):.1f}%\n")
                    f.write(f"- **Total Trades**: {best.get('total_trades', 0)}\n\n")
                    
                    f.write(f"### 📉 Worst Combination: {worst.get('timing_name', 'N/A')}\n")
                    f.write(f"- **Return**: {worst.get('total_return', 0):.2f}%\n")
                    f.write(f"- **Win Rate**: {worst.get('win_rate', 0):.1f}%\n")
                    f.write(f"- **Total Trades**: {worst.get('total_trades', 0)}\n\n")
                
                # Rankings
                if 'rankings' in analysis:
                    f.write(f"## Performance Rankings\n\n")
                    
                    for ranking_name, ranking_df in analysis['rankings'].items():
                        f.write(f"### {ranking_name.replace('_', ' ').title()}\n\n")
                        f.write(ranking_df.to_markdown(index=False))
                        f.write(f"\n\n")
            
            # Save detailed results as JSON
            json_path = os.path.join(report_dir, f"timing_results_{timestamp}.json")
            with open(json_path, 'w') as f:
                # Convert DataFrame to dict for JSON serialization
                json_data = analysis.copy()
                if 'all_results' in json_data:
                    json_data['all_results'] = json_data['all_results'].to_dict('records')
                if 'rankings' in json_data:
                    for key, df in json_data['rankings'].items():
                        json_data['rankings'][key] = df.to_dict('records')
                
                json.dump(json_data, f, indent=2, default=str)
            
            self.logger.info(f"📊 Timing analysis report saved: {report_path}")
            self.logger.info(f"📊 Detailed results saved: {json_path}")
            
            return report_path
            
        except Exception as e:
            self.logger.error(f"Error generating timing report: {e}")
            return ""
