"""
Volatility Regime Filter for SPX Overnight Options Strategy
Implements VIX-based filtering to avoid trading during unfavorable volatility conditions
"""

from typing import Optional, Dict, Tuple
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class VolatilityRegimeFilter:
    """
    Filters trading signals based on volatility regime using VIX data
    """
    
    def __init__(self, data_retriever):
        """
        Initialize volatility filter
        
        Args:
            data_retriever: PolygonDataRetriever instance for VIX data
        """
        self.data_retriever = data_retriever
        
        # Volatility thresholds from environment or defaults
        self.vix_low_threshold = float(os.getenv('VIX_LOW_THRESHOLD', '15'))      # Below 15: Low vol regime
        self.vix_high_threshold = float(os.getenv('VIX_HIGH_THRESHOLD', '25'))    # Above 25: High vol regime
        self.vix_extreme_threshold = float(os.getenv('VIX_EXTREME_THRESHOLD', '35'))  # Above 35: Extreme vol
        
        # Moving average parameters
        self.vix_ma_window = int(os.getenv('VIX_MA_WINDOW', '20'))  # 20-day moving average
        
        # Filter settings
        self.enable_vix_filter = os.getenv('ENABLE_VIX_FILTER', 'true').lower() == 'true'
        self.trade_in_low_vol = os.getenv('TRADE_IN_LOW_VOL', 'true').lower() == 'true'
        self.trade_in_medium_vol = os.getenv('TRADE_IN_MEDIUM_VOL', 'true').lower() == 'true'
        self.trade_in_high_vol = os.getenv('TRADE_IN_HIGH_VOL', 'false').lower() == 'true'
        self.trade_in_extreme_vol = os.getenv('TRADE_IN_EXTREME_VOL', 'false').lower() == 'true'
    
    def get_volatility_regime(self, date: str) -> Tuple[str, Dict]:
        """
        Determine current volatility regime based on VIX
        
        Args:
            date: Current date (YYYY-MM-DD)
            
        Returns:
            Tuple of (regime_name, regime_data)
        """
        try:
            # Get current VIX level
            vix_current = self.data_retriever.get_vix_price(date)
            if vix_current is None:
                return "unknown", {"error": "Could not get VIX data"}
            
            # Get VIX moving average for trend analysis
            vix_ma = self.data_retriever.get_vix_moving_average(date, self.vix_ma_window)
            
            # Determine regime based on absolute VIX level
            if vix_current >= self.vix_extreme_threshold:
                regime = "extreme_vol"
            elif vix_current >= self.vix_high_threshold:
                regime = "high_vol"
            elif vix_current <= self.vix_low_threshold:
                regime = "low_vol"
            else:
                regime = "medium_vol"
            
            # Additional analysis
            vix_vs_ma = None
            if vix_ma:
                vix_vs_ma = "above_ma" if vix_current > vix_ma else "below_ma"
            
            regime_data = {
                "vix_current": vix_current,
                "vix_ma": vix_ma,
                "vix_vs_ma": vix_vs_ma,
                "regime": regime,
                "thresholds": {
                    "low": self.vix_low_threshold,
                    "high": self.vix_high_threshold,
                    "extreme": self.vix_extreme_threshold
                }
            }
            
            return regime, regime_data
            
        except Exception as e:
            print(f"Error determining volatility regime for {date}: {e}")
            return "unknown", {"error": str(e)}
    
    def should_trade(self, date: str) -> Tuple[bool, str]:
        """
        Determine if trading should be allowed based on volatility regime
        
        Args:
            date: Current date (YYYY-MM-DD)
            
        Returns:
            Tuple of (should_trade, reason)
        """
        try:
            # If filter is disabled, always allow trading
            if not self.enable_vix_filter:
                return True, "VIX filter disabled"
            
            # Get volatility regime
            regime, regime_data = self.get_volatility_regime(date)
            
            if regime == "unknown":
                # If we can't determine regime, default to allowing trade
                return True, f"Unknown volatility regime: {regime_data.get('error', 'Unknown error')}"
            
            vix_current = regime_data.get("vix_current", 0)
            
            # Apply regime-based filtering
            if regime == "low_vol" and self.trade_in_low_vol:
                return True, f"Low volatility regime (VIX: {vix_current:.1f}) - Trading allowed"
            
            elif regime == "medium_vol" and self.trade_in_medium_vol:
                return True, f"Medium volatility regime (VIX: {vix_current:.1f}) - Trading allowed"
            
            elif regime == "high_vol" and self.trade_in_high_vol:
                return True, f"High volatility regime (VIX: {vix_current:.1f}) - Trading allowed"
            
            elif regime == "extreme_vol" and self.trade_in_extreme_vol:
                return True, f"Extreme volatility regime (VIX: {vix_current:.1f}) - Trading allowed"
            
            else:
                return False, f"Trading filtered out - {regime} (VIX: {vix_current:.1f})"
            
        except Exception as e:
            print(f"Error in volatility filter for {date}: {e}")
            # Default to allowing trade if there's an error
            return True, f"Filter error: {e}"
    
    def get_position_size_adjustment(self, date: str, base_size: float) -> Tuple[float, str]:
        """
        Adjust position size based on volatility regime
        
        Args:
            date: Current date (YYYY-MM-DD)
            base_size: Base position size (0.0 to 1.0)
            
        Returns:
            Tuple of (adjusted_size, reason)
        """
        try:
            regime, regime_data = self.get_volatility_regime(date)
            
            if regime == "unknown":
                return base_size, "No volatility adjustment - unknown regime"
            
            vix_current = regime_data.get("vix_current", 20)
            
            # Adjust position size based on volatility
            if regime == "low_vol":
                # Increase size in low volatility (options are cheaper)
                adjusted_size = min(base_size * 1.2, 0.5)  # Max 50% of capital
                return adjusted_size, f"Increased size for low vol (VIX: {vix_current:.1f})"
            
            elif regime == "medium_vol":
                # Keep normal size
                return base_size, f"Normal size for medium vol (VIX: {vix_current:.1f})"
            
            elif regime == "high_vol":
                # Reduce size in high volatility
                adjusted_size = base_size * 0.7
                return adjusted_size, f"Reduced size for high vol (VIX: {vix_current:.1f})"
            
            elif regime == "extreme_vol":
                # Significantly reduce size in extreme volatility
                adjusted_size = base_size * 0.5
                return adjusted_size, f"Significantly reduced size for extreme vol (VIX: {vix_current:.1f})"
            
            else:
                return base_size, f"No adjustment for regime: {regime}"
            
        except Exception as e:
            print(f"Error adjusting position size for {date}: {e}")
            return base_size, f"No adjustment due to error: {e}"
    
    def get_filter_summary(self) -> Dict:
        """
        Get summary of current filter settings
        
        Returns:
            Dictionary with filter configuration
        """
        return {
            "enabled": self.enable_vix_filter,
            "thresholds": {
                "low_vol": self.vix_low_threshold,
                "high_vol": self.vix_high_threshold,
                "extreme_vol": self.vix_extreme_threshold
            },
            "trading_allowed": {
                "low_vol": self.trade_in_low_vol,
                "medium_vol": self.trade_in_medium_vol,
                "high_vol": self.trade_in_high_vol,
                "extreme_vol": self.trade_in_extreme_vol
            },
            "ma_window": self.vix_ma_window
        }

