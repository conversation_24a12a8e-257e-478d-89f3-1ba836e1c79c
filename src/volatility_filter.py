"""
Refactored Volatility Regime Filter for SPX Overnight Options Strategy
Implements VIX-based filtering to avoid trading during unfavorable volatility conditions
"""

from typing import Optional, Dict, Tuple, NamedTuple
from datetime import datetime, timedelta
import logging
from enum import Enum

from config import StrategyConfiguration
from config_manager import load_config


class VolatilityRegime(Enum):
    """Enumeration of volatility regimes"""
    LOW = "low_vol"
    MEDIUM = "medium_vol"
    HIGH = "high_vol"
    EXTREME = "extreme_vol"
    UNKNOWN = "unknown"


class VolatilityData(NamedTuple):
    """Container for volatility analysis data"""
    vix_current: float
    regime: VolatilityRegime
    threshold_low: float
    threshold_medium: float
    threshold_high: float
    date: str


class TradingDecision(NamedTuple):
    """Container for trading decision data"""
    should_trade: bool
    reason: str
    regime: VolatilityRegime
    vix_value: float


class VolatilityRegimeFilter:
    """
    Filters trading signals based on volatility regime using VIX data
    """
    
    def __init__(self, data_retriever, config: Optional[StrategyConfiguration] = None):
        """
        Initialize volatility filter
        
        Args:
            data_retriever: PolygonDataRetriever instance for VIX data
            config: Strategy configuration. If None, loads from environment
        """
        self.data_retriever = data_retriever
        self.config = config or load_config()
        self.vf_config = self.config.volatility_filter
        
        # Set up logging
        self.logger = self._setup_logger()
        
        # Cache for VIX data to avoid repeated API calls
        self._vix_cache: Dict[str, float] = {}
    
    def _setup_logger(self) -> logging.Logger:
        """Set up logging for volatility filter"""
        logger = logging.getLogger('volatility_filter')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def get_vix_value(self, date: str) -> Optional[float]:
        """
        Get VIX value for a specific date with caching
        
        Args:
            date: Date in YYYY-MM-DD format
            
        Returns:
            VIX value or None if not available
        """
        # Check cache first
        if date in self._vix_cache:
            return self._vix_cache[date]
        
        try:
            vix_value = self.data_retriever.get_vix_data(date)
            
            # Cache the result
            if vix_value is not None:
                self._vix_cache[date] = vix_value
            
            return vix_value
            
        except Exception as e:
            self.logger.error(f"Error retrieving VIX data for {date}: {e}")
            return None
    
    def determine_volatility_regime(self, date: str) -> VolatilityData:
        """
        Determine current volatility regime based on VIX
        
        Args:
            date: Current date (YYYY-MM-DD)
            
        Returns:
            VolatilityData with regime information
        """
        vix_current = self.get_vix_value(date)
        
        if vix_current is None:
            self.logger.warning(f"No VIX data available for {date}")
            return VolatilityData(
                vix_current=0.0,
                regime=VolatilityRegime.UNKNOWN,
                threshold_low=self.vf_config.vix_low_threshold,
                threshold_medium=self.vf_config.vix_medium_threshold,
                threshold_high=self.vf_config.vix_high_threshold,
                date=date
            )
        
        # Determine regime based on thresholds
        if vix_current < self.vf_config.vix_low_threshold:
            regime = VolatilityRegime.LOW
        elif vix_current < self.vf_config.vix_medium_threshold:
            regime = VolatilityRegime.MEDIUM
        elif vix_current < self.vf_config.vix_high_threshold:
            regime = VolatilityRegime.HIGH
        else:
            regime = VolatilityRegime.EXTREME
        
        self.logger.debug(f"VIX regime for {date}: {regime.value} (VIX: {vix_current:.2f})")
        
        return VolatilityData(
            vix_current=vix_current,
            regime=regime,
            threshold_low=self.vf_config.vix_low_threshold,
            threshold_medium=self.vf_config.vix_medium_threshold,
            threshold_high=self.vf_config.vix_high_threshold,
            date=date
        )
    
    def should_trade(self, date: str) -> TradingDecision:
        """
        Determine if trading should be allowed based on volatility regime
        
        Args:
            date: Current date (YYYY-MM-DD)
            
        Returns:
            TradingDecision with recommendation and reasoning
        """
        try:
            # If filter is disabled, always allow trading
            if not self.vf_config.enable_vix_filter:
                return TradingDecision(
                    should_trade=True,
                    reason="VIX filter disabled",
                    regime=VolatilityRegime.UNKNOWN,
                    vix_value=0.0
                )
            
            # Get volatility regime
            vol_data = self.determine_volatility_regime(date)
            
            if vol_data.regime == VolatilityRegime.UNKNOWN:
                # If we can't determine regime, default to allowing trade
                return TradingDecision(
                    should_trade=True,
                    reason="Unknown volatility regime - defaulting to allow trading",
                    regime=vol_data.regime,
                    vix_value=vol_data.vix_current
                )
            
            # Apply regime-based filtering
            should_trade, reason = self._evaluate_trading_permission(vol_data)
            
            return TradingDecision(
                should_trade=should_trade,
                reason=reason,
                regime=vol_data.regime,
                vix_value=vol_data.vix_current
            )
            
        except Exception as e:
            self.logger.error(f"Error in volatility filter for {date}: {e}")
            # Default to allowing trade on error
            return TradingDecision(
                should_trade=True,
                reason=f"Error in volatility filter: {e}",
                regime=VolatilityRegime.UNKNOWN,
                vix_value=0.0
            )
    
    def _evaluate_trading_permission(self, vol_data: VolatilityData) -> Tuple[bool, str]:
        """
        Evaluate whether trading should be allowed based on volatility regime
        
        Args:
            vol_data: Volatility data for evaluation
            
        Returns:
            Tuple of (should_trade, reason)
        """
        regime = vol_data.regime
        vix_value = vol_data.vix_current
        
        if regime == VolatilityRegime.LOW and self.vf_config.trade_in_low_vol:
            return True, f"Low volatility regime (VIX: {vix_value:.1f}) - Trading allowed"
        
        elif regime == VolatilityRegime.MEDIUM and self.vf_config.trade_in_medium_vol:
            return True, f"Medium volatility regime (VIX: {vix_value:.1f}) - Trading allowed"
        
        elif regime == VolatilityRegime.HIGH and self.vf_config.trade_in_high_vol:
            return True, f"High volatility regime (VIX: {vix_value:.1f}) - Trading allowed"
        
        elif regime == VolatilityRegime.EXTREME and self.vf_config.trade_in_extreme_vol:
            return True, f"Extreme volatility regime (VIX: {vix_value:.1f}) - Trading allowed"
        
        else:
            return False, f"{regime.value.replace('_', ' ').title()} regime (VIX: {vix_value:.1f}) - Trading filtered out"
    
    def get_regime_statistics(self, start_date: str, end_date: str) -> Dict[str, int]:
        """
        Get statistics on volatility regimes over a date range
        
        Args:
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            Dictionary with regime counts
        """
        regime_counts = {regime.value: 0 for regime in VolatilityRegime}
        
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            
            current_dt = start_dt
            while current_dt <= end_dt:
                date_str = current_dt.strftime('%Y-%m-%d')
                vol_data = self.determine_volatility_regime(date_str)
                regime_counts[vol_data.regime.value] += 1
                current_dt += timedelta(days=1)
            
            self.logger.info(f"Volatility regime statistics from {start_date} to {end_date}: {regime_counts}")
            return regime_counts
            
        except Exception as e:
            self.logger.error(f"Error calculating regime statistics: {e}")
            return regime_counts
    
    def clear_cache(self) -> None:
        """Clear the VIX data cache"""
        self._vix_cache.clear()
        self.logger.info("VIX data cache cleared")
    
    # Backward compatibility methods
    def get_volatility_regime(self, date: str) -> Tuple[str, Dict]:
        """
        Backward compatibility method for get_volatility_regime
        
        Args:
            date: Current date (YYYY-MM-DD)
            
        Returns:
            Tuple of (regime_string, regime_data_dict)
        """
        vol_data = self.determine_volatility_regime(date)
        
        regime_data = {
            "vix_current": vol_data.vix_current,
            "vix_low_threshold": vol_data.threshold_low,
            "vix_medium_threshold": vol_data.threshold_medium,
            "vix_high_threshold": vol_data.threshold_high,
            "date": vol_data.date
        }
        
        return vol_data.regime.value, regime_data
