"""
Refactored Data Retrieval Module for SPX Overnight Options Strategy
Handles all Polygon.io API interactions for options and underlying data
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
from typing import Dict, List, Optional, Tuple, Union
import pytz
import logging
from dataclasses import dataclass

from config import StrategyConfiguration
from config_manager import load_config


@dataclass
class MarketDataPoint:
    """Represents a single market data point"""
    timestamp: datetime
    price: float
    volume: Optional[int] = None
    symbol: str = ""
    
    def is_valid(self, min_price: float = 0.01, max_price: float = 10000.0) -> bool:
        """Validate the data point"""
        return (
            self.price is not None and
            min_price <= self.price <= max_price and
            self.timestamp is not None
        )


@dataclass
class OptionsContract:
    """Represents an options contract with all relevant details"""
    ticker: str
    underlying_symbol: str
    contract_type: str  # 'call' or 'put'
    strike_price: float
    expiration_date: str
    shares_per_contract: int = 100
    last_price: Optional[float] = None
    bid: Optional[float] = None
    ask: Optional[float] = None
    volume: Optional[int] = None
    open_interest: Optional[int] = None
    implied_volatility: Optional[float] = None
    
    def is_valid(self) -> bool:
        """Validate the options contract data"""
        return (
            self.ticker and
            self.underlying_symbol and
            self.contract_type in ['call', 'put'] and
            self.strike_price > 0 and
            self.expiration_date and
            self.shares_per_contract > 0
        )
    
    def to_dict(self) -> Dict:
        """Convert to dictionary format for backward compatibility"""
        return {
            'details': {
                'ticker': self.ticker,
                'contract_type': self.contract_type,
                'strike_price': self.strike_price,
                'expiration_date': self.expiration_date,
                'shares_per_contract': self.shares_per_contract
            },
            'last_price': self.last_price,
            'bid': self.bid,
            'ask': self.ask,
            'volume': self.volume,
            'open_interest': self.open_interest,
            'implied_volatility': self.implied_volatility
        }


class DataRetrievalError(Exception):
    """Custom exception for data retrieval errors"""
    pass


class PolygonDataRetriever:
    """
    Handles data retrieval from Polygon.io API for SPX options strategy
    """
    
    def __init__(self, config: Optional[StrategyConfiguration] = None):
        """
        Initialize the data retriever with configuration
        
        Args:
            config: Strategy configuration. If None, loads from environment
        """
        self.config = config or load_config()
        self.api_config = self.config.api
        self.data_config = self.config.data
        
        # Set up session and rate limiting
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'SPX-Overnight-Strategy/1.0'
        })
        
        # Timezone setup
        self.et_tz = pytz.timezone(self.data_config.market_timezone)
        self.utc_tz = pytz.UTC
        
        # Set up logging
        self.logger = self._setup_logger()
        
        # Data validation parameters
        self.min_underlying_price = self.data_config.min_underlying_price
        self.max_underlying_price = self.data_config.max_underlying_price
        self.max_price_change = self.data_config.max_price_change
    
    def _setup_logger(self) -> logging.Logger:
        """Set up logging for data retrieval"""
        logger = logging.getLogger('data_retrieval')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """
        Make API request with rate limiting and error handling
        
        Args:
            endpoint: API endpoint path
            params: Query parameters
            
        Returns:
            JSON response data or None if failed
            
        Raises:
            DataRetrievalError: If request fails after retries
        """
        if params is None:
            params = {}
        
        params['apikey'] = self.api_config.polygon_api_key
        url = f"{self.api_config.base_url}{endpoint}"
        
        for attempt in range(self.api_config.max_retries):
            try:
                # Rate limiting
                time.sleep(self.api_config.rate_limit_delay)
                
                response = self.session.get(
                    url, 
                    params=params, 
                    timeout=self.api_config.timeout_seconds
                )
                response.raise_for_status()
                
                data = response.json()
                
                if data.get('status') == 'OK':
                    return data
                else:
                    error_msg = data.get('error', 'Unknown API error')
                    self.logger.warning(f"API Error (attempt {attempt + 1}): {error_msg}")
                    if attempt == self.api_config.max_retries - 1:
                        raise DataRetrievalError(f"API Error: {error_msg}")
                    
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Request failed (attempt {attempt + 1}): {e}")
                if attempt == self.api_config.max_retries - 1:
                    raise DataRetrievalError(f"Request failed after {self.api_config.max_retries} attempts: {e}")
                    
            except Exception as e:
                self.logger.error(f"Unexpected error: {e}")
                raise DataRetrievalError(f"Unexpected error: {e}")
        
        return None
    
    def get_underlying_price(self, date: str, symbol: Optional[str] = None) -> Optional[float]:
        """
        Get underlying asset price for a specific date
        
        Args:
            date: Date in YYYY-MM-DD format
            symbol: Symbol to retrieve (defaults to config value)
            
        Returns:
            Closing price or None if not available
            
        Raises:
            DataRetrievalError: If data retrieval fails
        """
        try:
            # Validate date format
            datetime.strptime(date, '%Y-%m-%d')
            
            # Use symbol from config if not provided
            symbol = symbol or self.config.strategy.underlying_symbol
            
            # Get daily bar for the date
            endpoint = f"/v2/aggs/ticker/I:{symbol}/range/1/day/{date}/{date}"
            data = self._make_request(endpoint)
            
            if data and data.get('results') and len(data['results']) > 0:
                price = data['results'][0]['c']  # closing price
                
                # Validate price
                if self._validate_underlying_price(price):
                    self.logger.debug(f"Retrieved {symbol} price for {date}: ${price:.2f}")
                    return price
                else:
                    self.logger.warning(f"Invalid {symbol} price for {date}: ${price:.2f}")
                    return None
            
            self.logger.warning(f"No {symbol} price data available for {date}")
            return None
            
        except ValueError as e:
            self.logger.error(f"Invalid date format: {date}")
            raise DataRetrievalError(f"Invalid date format: {date}")
        except DataRetrievalError:
            raise
        except Exception as e:
            self.logger.error(f"Error getting {symbol} price for {date}: {e}")
            raise DataRetrievalError(f"Failed to get {symbol} price: {e}")
    
    def _validate_underlying_price(self, price: float) -> bool:
        """
        Validate underlying asset price
        
        Args:
            price: Price to validate
            
        Returns:
            True if price is valid
        """
        return (
            price is not None and
            self.min_underlying_price <= price <= self.max_underlying_price
        )
    
    # Backward compatibility method
    def get_spx_price(self, date: str) -> Optional[float]:
        """
        Get SPX index price for a specific date (backward compatibility)

        Args:
            date: Date in YYYY-MM-DD format

        Returns:
            SPX closing price or None if not available
        """
        return self.get_underlying_price(date, 'SPX')

    def get_options_chain(self,
                         underlying: Optional[str] = None,
                         expiration_date: Optional[str] = None,
                         contract_type: Optional[str] = None) -> List[OptionsContract]:
        """
        Get options chain for the underlying asset

        Args:
            underlying: Underlying symbol (defaults to config value)
            expiration_date: Expiration date filter (YYYY-MM-DD)
            contract_type: "call" or "put" (defaults to config value)

        Returns:
            List of validated options contracts

        Raises:
            DataRetrievalError: If data retrieval fails
        """
        try:
            # Use defaults from configuration
            underlying = underlying or self.config.strategy.underlying_symbol
            contract_type = contract_type or self.config.strategy.contract_type

            # Validate inputs
            if contract_type not in ['call', 'put']:
                raise ValueError(f"Invalid contract type: {contract_type}")

            if expiration_date:
                datetime.strptime(expiration_date, '%Y-%m-%d')  # Validate date format

            # For historical backtesting, use the contracts endpoint
            endpoint = "/v3/reference/options/contracts"
            params = {
                'underlying_ticker': underlying,
                'contract_type': contract_type,
                'limit': 250  # Maximum allowed
            }

            if expiration_date:
                params['expiration_date'] = expiration_date

            data = self._make_request(endpoint, params)

            if data and data.get('results'):
                contracts = []
                for contract_data in data['results']:
                    try:
                        contract = self._parse_options_contract(contract_data)
                        if contract and contract.is_valid():
                            contracts.append(contract)
                        else:
                            self.logger.debug(f"Invalid contract data: {contract_data}")
                    except Exception as e:
                        self.logger.warning(f"Failed to parse contract: {e}")
                        continue

                self.logger.info(f"Retrieved {len(contracts)} valid options contracts")
                return contracts

            self.logger.warning("No options chain data available")
            return []

        except ValueError as e:
            self.logger.error(f"Invalid input parameters: {e}")
            raise DataRetrievalError(f"Invalid parameters: {e}")
        except DataRetrievalError:
            raise
        except Exception as e:
            self.logger.error(f"Error getting options chain: {e}")
            raise DataRetrievalError(f"Failed to get options chain: {e}")

    def _parse_options_contract(self, contract_data: Dict) -> Optional[OptionsContract]:
        """
        Parse raw contract data into OptionsContract object

        Args:
            contract_data: Raw contract data from API

        Returns:
            Parsed OptionsContract or None if parsing fails
        """
        try:
            return OptionsContract(
                ticker=contract_data.get('ticker', ''),
                underlying_symbol=contract_data.get('underlying_ticker', ''),
                contract_type=contract_data.get('contract_type', ''),
                strike_price=float(contract_data.get('strike_price', 0)),
                expiration_date=contract_data.get('expiration_date', ''),
                shares_per_contract=int(contract_data.get('shares_per_contract', 100))
            )

        except (ValueError, TypeError, KeyError) as e:
            self.logger.warning(f"Failed to parse contract data: {e}")
            return None

    def find_atm_option(self, underlying_price: float,
                       options_chain: List[OptionsContract],
                       contract_type: Optional[str] = None) -> Optional[OptionsContract]:
        """
        Find the at-the-money (ATM) option from the chain

        Args:
            underlying_price: Current price of underlying asset
            options_chain: List of options contracts
            contract_type: Filter by contract type (defaults to config value)

        Returns:
            ATM option contract or None
        """
        if not options_chain:
            return None

        contract_type = contract_type or self.config.strategy.contract_type

        # Filter by contract type and find closest strike
        min_diff = float('inf')
        atm_option = None

        for option in options_chain:
            if option.contract_type == contract_type:
                diff = abs(option.strike_price - underlying_price)
                if diff < min_diff:
                    min_diff = diff
                    atm_option = option

        if atm_option:
            self.logger.debug(f"Found ATM {contract_type} option: {atm_option.ticker} "
                            f"(strike: ${atm_option.strike_price:.2f}, diff: ${min_diff:.2f})")

        return atm_option

    def get_option_price_at_time(self, option_ticker: str, date: str,
                                time_str: str) -> Optional[float]:
        """
        Get option price at a specific time on a specific date

        Args:
            option_ticker: Options ticker symbol (e.g., O:SPX240315C05000000)
            date: Date in YYYY-MM-DD format
            time_str: Time in HH:MM format (ET)

        Returns:
            Option price or None if not available

        Raises:
            DataRetrievalError: If data retrieval fails
        """
        try:
            # Validate inputs
            datetime.strptime(date, '%Y-%m-%d')
            datetime.strptime(time_str, '%H:%M')

            # For historical data, we'll try to get the closest available price
            # This is a simplified implementation - in practice, you might need
            # to use minute-level data or snapshots
            endpoint = f"/v2/aggs/ticker/{option_ticker}/range/1/day/{date}/{date}"
            data = self._make_request(endpoint)

            if data and data.get('results') and len(data['results']) > 0:
                # Use closing price as approximation
                price = data['results'][0]['c']

                if price > 0:
                    self.logger.debug(f"Retrieved option price for {option_ticker} on {date}: ${price:.2f}")
                    return price

            self.logger.warning(f"No option price data available for {option_ticker} on {date}")
            return None

        except ValueError as e:
            self.logger.error(f"Invalid input format: {e}")
            raise DataRetrievalError(f"Invalid input format: {e}")
        except DataRetrievalError:
            raise
        except Exception as e:
            self.logger.error(f"Error getting option price: {e}")
            raise DataRetrievalError(f"Failed to get option price: {e}")

    def get_vix_data(self, date: str) -> Optional[float]:
        """
        Get VIX data for volatility filtering

        Args:
            date: Date in YYYY-MM-DD format

        Returns:
            VIX closing value or None if not available

        Raises:
            DataRetrievalError: If data retrieval fails
        """
        try:
            # Validate date format
            datetime.strptime(date, '%Y-%m-%d')

            # Get VIX daily bar for the date
            vix_symbol = self.config.volatility_filter.vix_symbol
            endpoint = f"/v2/aggs/ticker/{vix_symbol}/range/1/day/{date}/{date}"
            data = self._make_request(endpoint)

            if data and data.get('results') and len(data['results']) > 0:
                vix_value = data['results'][0]['c']  # closing value

                if vix_value > 0:
                    self.logger.debug(f"Retrieved VIX value for {date}: {vix_value:.2f}")
                    return vix_value

            # Try looking back a few days if current day not available
            for days_back in range(1, self.config.volatility_filter.vix_lookback_days + 1):
                try:
                    lookback_date = (datetime.strptime(date, '%Y-%m-%d') - timedelta(days=days_back)).strftime('%Y-%m-%d')
                    endpoint = f"/v2/aggs/ticker/{vix_symbol}/range/1/day/{lookback_date}/{lookback_date}"
                    data = self._make_request(endpoint)

                    if data and data.get('results') and len(data['results']) > 0:
                        vix_value = data['results'][0]['c']
                        if vix_value > 0:
                            self.logger.info(f"Using VIX value from {lookback_date}: {vix_value:.2f}")
                            return vix_value
                except Exception:
                    continue

            self.logger.warning(f"No VIX data available for {date} or lookback period")
            return None

        except ValueError as e:
            self.logger.error(f"Invalid date format: {date}")
            raise DataRetrievalError(f"Invalid date format: {date}")
        except DataRetrievalError:
            raise
        except Exception as e:
            self.logger.error(f"Error getting VIX data for {date}: {e}")
            raise DataRetrievalError(f"Failed to get VIX data: {e}")
