"""
Configuration Manager for SPX Overnight Options Strategy
Provides utilities for loading, validating, and managing configuration
"""

import json
import os
from typing import Dict, Any, Optional
from datetime import datetime
import logging

from config import StrategyConfiguration


class ConfigurationManager:
    """Manages configuration loading, validation, and persistence"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize configuration manager
        
        Args:
            config_file: Optional path to JSON configuration file
        """
        self.config_file = config_file
        self.config: Optional[StrategyConfiguration] = None
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """Set up logging for configuration management"""
        logger = logging.getLogger('config_manager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def load_configuration(self) -> StrategyConfiguration:
        """
        Load configuration from environment variables and optional config file
        
        Returns:
            Loaded and validated configuration
        """
        try:
            # Start with environment-based configuration
            self.config = StrategyConfiguration.load_from_env()
            
            # Override with config file if provided
            if self.config_file and os.path.exists(self.config_file):
                self._load_from_file()
            
            # Validate configuration
            errors = self.config.validate()
            if errors:
                error_msg = "Configuration validation failed:\n" + "\n".join(f"- {error}" for error in errors)
                raise ValueError(error_msg)
            
            self.logger.info("Configuration loaded and validated successfully")
            return self.config
            
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            raise
    
    def _load_from_file(self) -> None:
        """Load configuration overrides from JSON file"""
        try:
            with open(self.config_file, 'r') as f:
                config_data = json.load(f)
            
            # Apply overrides to configuration
            self._apply_config_overrides(config_data)
            self.logger.info(f"Configuration overrides loaded from {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to load config file {self.config_file}: {e}")
            raise
    
    def _apply_config_overrides(self, config_data: Dict[str, Any]) -> None:
        """Apply configuration overrides from dictionary"""
        for section_name, section_data in config_data.items():
            if hasattr(self.config, section_name):
                section = getattr(self.config, section_name)
                for key, value in section_data.items():
                    if hasattr(section, key):
                        setattr(section, key, value)
                        self.logger.debug(f"Override: {section_name}.{key} = {value}")
                    else:
                        self.logger.warning(f"Unknown config key: {section_name}.{key}")
            else:
                self.logger.warning(f"Unknown config section: {section_name}")
    
    def save_configuration(self, output_file: str) -> None:
        """
        Save current configuration to JSON file
        
        Args:
            output_file: Path to save configuration
        """
        if not self.config:
            raise ValueError("No configuration loaded")
        
        try:
            config_dict = self.config.to_dict()
            
            with open(output_file, 'w') as f:
                json.dump(config_dict, f, indent=2, default=str)
            
            self.logger.info(f"Configuration saved to {output_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
            raise
    
    def print_configuration(self) -> None:
        """Print current configuration in a readable format"""
        if not self.config:
            print("No configuration loaded")
            return
        
        print("=" * 60)
        print("SPX OVERNIGHT STRATEGY CONFIGURATION")
        print("=" * 60)
        
        sections = [
            ("API Configuration", self.config.api),
            ("Strategy Parameters", self.config.strategy),
            ("Risk Management", self.config.risk_management),
            ("Volatility Filter", self.config.volatility_filter),
            ("Pricing Configuration", self.config.pricing),
            ("Data Configuration", self.config.data),
            ("Reporting Configuration", self.config.reporting)
        ]
        
        for section_name, section_obj in sections:
            print(f"\n{section_name}:")
            print("-" * len(section_name))
            
            for key, value in section_obj.__dict__.items():
                # Mask sensitive information
                if 'key' in key.lower() or 'password' in key.lower():
                    if isinstance(value, str) and len(value) > 4:
                        display_value = f"{'*' * (len(value) - 4)}{value[-4:]}"
                    else:
                        display_value = "****"
                else:
                    display_value = value
                
                print(f"  {key}: {display_value}")
    
    def get_config(self) -> StrategyConfiguration:
        """
        Get current configuration, loading if necessary
        
        Returns:
            Current configuration
        """
        if not self.config:
            self.load_configuration()
        return self.config
    
    def create_sample_config_file(self, output_file: str) -> None:
        """
        Create a sample configuration file with all available options
        
        Args:
            output_file: Path to save sample configuration
        """
        sample_config = {
            "strategy": {
                "underlying_symbol": "SPX",
                "contract_type": "call",
                "entry_time": "15:30",
                "exit_time": "10:00",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                "initial_capital": 100000,
                "preferred_days_to_expiration": 60
            },
            "risk_management": {
                "max_position_size": 0.35,
                "max_daily_loss": 0.05,
                "enable_stop_loss": False,
                "stop_loss_percentage": 0.50
            },
            "volatility_filter": {
                "enable_vix_filter": True,
                "vix_low_threshold": 12.0,
                "vix_medium_threshold": 20.0,
                "vix_high_threshold": 30.0,
                "trade_in_low_vol": True,
                "trade_in_medium_vol": True,
                "trade_in_high_vol": False,
                "trade_in_extreme_vol": False
            },
            "pricing": {
                "risk_free_rate": 0.05,
                "default_volatility": 0.20,
                "strike_increment": 25.0
            },
            "reporting": {
                "generate_pdf": True,
                "generate_plots": True,
                "plot_dpi": 300
            }
        }
        
        try:
            with open(output_file, 'w') as f:
                json.dump(sample_config, f, indent=2)
            
            print(f"Sample configuration file created: {output_file}")
            print("Edit this file to customize your strategy parameters")
            
        except Exception as e:
            self.logger.error(f"Failed to create sample config file: {e}")
            raise


def load_config(config_file: Optional[str] = None) -> StrategyConfiguration:
    """
    Convenience function to load configuration
    
    Args:
        config_file: Optional path to JSON configuration file
        
    Returns:
        Loaded configuration
    """
    manager = ConfigurationManager(config_file)
    return manager.load_configuration()


def print_config(config_file: Optional[str] = None) -> None:
    """
    Convenience function to print configuration
    
    Args:
        config_file: Optional path to JSON configuration file
    """
    manager = ConfigurationManager(config_file)
    manager.load_configuration()
    manager.print_configuration()


if __name__ == "__main__":
    # Command-line interface for configuration management
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "print":
            config_file = sys.argv[2] if len(sys.argv) > 2 else None
            print_config(config_file)
        
        elif command == "validate":
            config_file = sys.argv[2] if len(sys.argv) > 2 else None
            try:
                config = load_config(config_file)
                print("✓ Configuration is valid")
            except Exception as e:
                print(f"✗ Configuration validation failed: {e}")
                sys.exit(1)
        
        elif command == "sample":
            output_file = sys.argv[2] if len(sys.argv) > 2 else "config_sample.json"
            manager = ConfigurationManager()
            manager.create_sample_config_file(output_file)
        
        else:
            print("Usage: python config_manager.py [print|validate|sample] [config_file]")
    else:
        print_config()
