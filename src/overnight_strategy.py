"""
SPX Overnight Options Strategy
Main strategy implementation for buying ATM calls at 3:30 PM and selling at 10:00 AM
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
import os
from dotenv import load_dotenv

from data_retrieval import PolygonDataRetriever
from position_manager import PositionManager, OptionsPosition
from option_pricing import SyntheticOptionsGenerator
from volatility_filter import VolatilityRegimeFilter

# Load environment variables
load_dotenv()

class SPXOvernightStrategy:
    """
    Implements the SPX overnight options strategy
    """
    
    def __init__(self, config=None, api_key: str = None, initial_capital: float = None):
        """
        Initialize the strategy

        Args:
            config: StrategyConfiguration object (preferred)
            api_key: Polygon.io API key (legacy support)
            initial_capital: Starting capital (legacy support)
        """
        # Handle both new config system and legacy parameters
        if config is not None:
            self.config = config
            api_key = config.api.polygon_api_key
            initial_capital = config.strategy.initial_capital
        else:
            # Legacy support
            api_key = api_key or os.getenv('POLYGON_API_KEY')
            initial_capital = initial_capital or float(os.getenv('INITIAL_CAPITAL', '100000'))
            # Create a minimal config for compatibility
            from config import StrategyConfiguration
            self.config = StrategyConfiguration.load_from_env()

        if not api_key:
            raise ValueError("Polygon API key is required")

        self.data_retriever = PolygonDataRetriever(self.config)
        self.position_manager = PositionManager(self.config)
        self.synthetic_generator = SyntheticOptionsGenerator(self.config)
        self.volatility_filter = VolatilityRegimeFilter(self.data_retriever, self.config)

        # Strategy parameters from config
        self.underlying_symbol = self.config.strategy.underlying_symbol
        self.entry_time = self.config.strategy.entry_time
        self.exit_time = self.config.strategy.exit_time
        self.contract_type = self.config.strategy.contract_type

        # Trading parameters from config
        self.min_days_to_expiration = self.config.strategy.min_days_to_expiration
        self.max_days_to_expiration = self.config.strategy.max_days_to_expiration
        
        # Results tracking
        self.backtest_results = []
        self.daily_performance = []
        
    def find_suitable_expiration(self, current_date: str) -> Optional[str]:
        """
        Find suitable expiration date for options (targeting ~60 days)
        
        Args:
            current_date: Current trading date (YYYY-MM-DD)
            
        Returns:
            Expiration date or None if not found
        """
        try:
            # Get expiration date targeting 60 days out
            next_exp = self.data_retriever.get_next_expiration_date(
                current_date, min_days=self.min_days_to_expiration, max_days=self.max_days_to_expiration
            )
            
            if not next_exp:
                return None
            
            # Check if expiration is within acceptable range (55-65 days)
            current_dt = datetime.strptime(current_date, '%Y-%m-%d')
            exp_dt = datetime.strptime(next_exp, '%Y-%m-%d')
            days_to_exp = (exp_dt - current_dt).days
            
            if self.min_days_to_expiration <= days_to_exp <= self.max_days_to_expiration:
                return next_exp
            
            return None
            
        except Exception as e:
            print(f"Error finding expiration date: {e}")
            return None
    
    def execute_entry_signal(self, trade_date: str) -> bool:
        """
        Execute entry signal at 3:30 PM
        
        Args:
            trade_date: Trading date (YYYY-MM-DD)
            
        Returns:
            True if entry executed successfully
        """
        try:
            print(f"=== Entry Signal: {trade_date} at {self.entry_time} ===")
            
            # Check volatility regime filter first
            trading_decision = self.volatility_filter.should_trade(trade_date)
            print(f"Volatility Filter: {trading_decision.reason}")

            if not trading_decision.should_trade:
                print(f"Trade filtered out by volatility regime")
                return False
            
            # Get SPX price at entry time
            spx_price = self.data_retriever.get_spx_price(trade_date)
            if spx_price is None:
                print(f"Could not get SPX price for {trade_date}")
                return False
            
            print(f"SPX price: ${spx_price:.2f}")
            
            # Find suitable expiration date
            expiration_date = self.find_suitable_expiration(trade_date)
            if not expiration_date:
                print(f"No suitable expiration date found for {trade_date}")
                return False
            
            print(f"Using expiration: {expiration_date}")
            
            # Get options chain
            options_chain = self.data_retriever.get_options_chain(
                underlying="I:SPX",
                expiration_date=expiration_date,
                contract_type=self.contract_type
            )
            
            # If no market data available, use synthetic options
            if not options_chain:
                print(f"No market options data, generating synthetic ATM option")
                synthetic_option = self.synthetic_generator.generate_atm_option(
                    spx_price, trade_date, expiration_date, self.contract_type
                )
                
                if not synthetic_option:
                    print(f"Could not generate synthetic option for {trade_date}")
                    return False
                
                # Use synthetic option
                atm_option = synthetic_option
                entry_price = synthetic_option.get('synthetic_price')
                
            else:
                # Find ATM option from market data
                atm_option = self.data_retriever.find_atm_option(spx_price, options_chain)
                if not atm_option:
                    print(f"Could not find ATM option for {trade_date}")
                    return False
                
                # Get option price at entry time
                # Note: Using last_quote midpoint as proxy for entry price
                last_quote = atm_option.get('last_quote', {})
                if last_quote:
                    bid = last_quote.get('bid', 0)
                    ask = last_quote.get('ask', 0)
                    if bid > 0 and ask > 0:
                        entry_price = (bid + ask) / 2  # Use midpoint
                    else:
                        entry_price = atm_option.get('day', {}).get('close')
                else:
                    entry_price = atm_option.get('day', {}).get('close')
            
            # Extract option details
            option_details = atm_option.get('details', {})
            option_ticker = option_details.get('ticker')
            strike_price = option_details.get('strike_price')
            
            if not option_ticker or not strike_price:
                print(f"Invalid option details for {trade_date}")
                return False
            
            print(f"ATM Option: {option_ticker}, Strike: ${strike_price:.2f}")
            
            if not entry_price or entry_price <= 0:
                print(f"Could not get valid entry price for {trade_date}")
                return False
            
            print(f"Entry price: ${entry_price:.2f}")
            
            # Calculate position size
            position_size = self.position_manager.calculate_position_size(
                entry_price, spx_price
            )
            print(f"Position size: {position_size} contracts")
            
            # Open position
            success = self.position_manager.open_position(
                ticker=option_ticker,
                contract_type=self.contract_type,
                strike_price=strike_price,
                expiration_date=expiration_date,
                entry_date=trade_date,
                entry_time=self.entry_time,
                entry_price=entry_price,
                underlying_price=spx_price,
                quantity=position_size
            )
            
            return success
            
        except Exception as e:
            print(f"Error executing entry signal for {trade_date}: {e}")
            return False
    
    def execute_exit_signal(self, trade_date: str) -> int:
        """
        Execute exit signal at 10:00 AM
        
        Args:
            trade_date: Trading date (YYYY-MM-DD)
            
        Returns:
            Number of positions closed
        """
        try:
            print(f"\n=== Exit Signal: {trade_date} at {self.exit_time} ===")
            
            # Get open positions
            open_positions = self.position_manager.get_open_positions()
            if not open_positions:
                print("No open positions to close")
                return 0
            
            # Get SPX price at exit time
            spx_price = self.data_retriever.get_spx_price(trade_date)
            if spx_price is None:
                print(f"Could not get SPX price for {trade_date}")
                return 0
            
            print(f"SPX price: ${spx_price:.2f}")
            
            # Get exit prices for all open positions
            exit_prices = {}
            for position in open_positions:
                # Try to get market data first
                exit_price = self.data_retriever.get_option_price_at_time(
                    position.ticker, trade_date, self.exit_time
                )
                
                # If no market data, use synthetic pricing
                if not exit_price or exit_price <= 0:
                    print(f"No market data for {position.ticker}, using synthetic pricing")
                    
                    # Calculate synthetic exit price
                    exit_price = self.synthetic_generator.bs_calculator.calculate_option_price(
                        underlying_price=spx_price,
                        strike_price=position.strike_price,
                        current_date=trade_date,
                        expiration_date=position.expiration_date,
                        option_type=position.contract_type
                    )
                
                if exit_price and exit_price > 0:
                    exit_prices[position.ticker] = exit_price
                    print(f"Exit price for {position.ticker}: ${exit_price:.2f}")
                else:
                    print(f"Could not get exit price for {position.ticker}")
            
            # Close all positions with valid exit prices
            closed_count = self.position_manager.close_all_positions(
                exit_date=trade_date,
                exit_time=self.exit_time,
                exit_prices=exit_prices,
                underlying_price=spx_price
            )
            
            return closed_count
            
        except Exception as e:
            print(f"Error executing exit signal for {trade_date}: {e}")
            return 0
    
    def run_single_day(self, trade_date: str) -> Dict:
        """
        Run strategy for a single trading day
        
        Args:
            trade_date: Trading date (YYYY-MM-DD)
            
        Returns:
            Dictionary with day's results
        """
        day_result = {
            'date': trade_date,
            'entry_executed': False,
            'positions_opened': 0,
            'positions_closed': 0,
            'daily_pnl': 0.0,
            'capital_before': self.position_manager.current_capital,
            'capital_after': 0.0
        }
        
        try:
            # Execute entry signal (buy at 3:30 PM)
            if self.execute_entry_signal(trade_date):
                day_result['entry_executed'] = True
                day_result['positions_opened'] = 1
            
            # Calculate next trading day for exit
            next_day = datetime.strptime(trade_date, '%Y-%m-%d') + timedelta(days=1)
            next_trade_date = next_day.strftime('%Y-%m-%d')
            
            # Skip weekends
            while not self.data_retriever.validate_trading_day(next_trade_date):
                next_day += timedelta(days=1)
                next_trade_date = next_day.strftime('%Y-%m-%d')
            
            # Execute exit signal (sell at 10:00 AM next day)
            positions_closed = self.execute_exit_signal(next_trade_date)
            day_result['positions_closed'] = positions_closed
            
            # Calculate daily P&L
            day_result['capital_after'] = self.position_manager.current_capital
            day_result['daily_pnl'] = (day_result['capital_after'] - 
                                     day_result['capital_before'])
            
            return day_result
            
        except Exception as e:
            print(f"Error running single day {trade_date}: {e}")
            day_result['capital_after'] = self.position_manager.current_capital
            return day_result
    
    def run_backtest(self, start_date: str, end_date: str) -> Dict:
        """
        Run full backtest over date range
        
        Args:
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            Backtest results dictionary
        """
        print(f"\n{'='*60}")
        print(f"STARTING SPX OVERNIGHT OPTIONS BACKTEST")
        print(f"Period: {start_date} to {end_date}")
        print(f"Initial Capital: ${self.position_manager.initial_capital:,.2f}")
        print(f"{'='*60}")
        
        try:
            # Generate trading dates
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            
            current_date = start_dt
            trading_days = []
            
            while current_date <= end_dt:
                date_str = current_date.strftime('%Y-%m-%d')
                if self.data_retriever.validate_trading_day(date_str):
                    trading_days.append(date_str)
                current_date += timedelta(days=1)
            
            print(f"Total trading days: {len(trading_days)}")
            
            # Run strategy for each trading day
            for i, trade_date in enumerate(trading_days):
                print(f"\nProgress: {i+1}/{len(trading_days)} ({((i+1)/len(trading_days)*100):.1f}%)")
                
                day_result = self.run_single_day(trade_date)
                self.daily_performance.append(day_result)
                
                # Print progress every 10 days
                if (i + 1) % 10 == 0:
                    current_capital = self.position_manager.current_capital
                    total_return = ((current_capital - self.position_manager.initial_capital) / 
                                  self.position_manager.initial_capital) * 100
                    print(f"Current Capital: ${current_capital:,.2f} (Return: {total_return:.2f}%)")
            
            # Generate final results
            performance_summary = self.position_manager.get_portfolio_summary()
            
            backtest_results = {
                'start_date': start_date,
                'end_date': end_date,
                'trading_days': len(trading_days),
                'performance': performance_summary,
                'daily_performance': self.daily_performance
            }
            
            self.backtest_results = backtest_results
            
            print(f"\n{'='*60}")
            print(f"BACKTEST COMPLETED")
            print(f"{'='*60}")
            
            return backtest_results
            
        except Exception as e:
            print(f"Error running backtest: {e}")
            return {}
    
    def print_performance_summary(self):
        """Print detailed performance summary"""
        if not self.backtest_results:
            print("No backtest results available")
            return
        
        perf = self.backtest_results['performance']
        
        print(f"\n{'='*60}")
        print(f"PERFORMANCE SUMMARY")
        print(f"{'='*60}")
        print(f"Initial Capital:      ${perf['initial_capital']:,.2f}")
        print(f"Final Capital:        ${perf['current_capital']:,.2f}")
        total_pnl = perf['current_capital'] - perf['initial_capital']
        print(f"Total P&L:            ${total_pnl:,.2f}")
        print(f"Total Return:         {perf['total_return_pct']:.2f}%")
        print(f"")
        print(f"Total Trades:         {perf['total_trades']}")
        print(f"Winning Trades:       {perf['winning_trades']}")
        print(f"Losing Trades:        {perf['losing_trades']}")
        print(f"Win Rate:             {perf['win_rate_pct']:.2f}%")
        print(f"")
        print(f"Average Win:          ${perf.get('avg_win', 0):,.2f}")
        print(f"Average Loss:         ${perf.get('avg_loss', 0):,.2f}")
        print(f"")
        print(f"Max Drawdown:         ${perf['max_drawdown']:,.2f}")
        print(f"Max Drawdown %:       {perf['max_drawdown_pct']:.2f}%")
        print(f"{'='*60}")

    def get_performance_summary(self) -> Dict:
        """
        Get performance summary dictionary

        Returns:
            Dictionary with performance metrics
        """
        if not self.backtest_results:
            return {}
        return self.backtest_results.get('performance', {})
    
    def export_results(self, output_dir: str = "report"):
        """
        Export backtest results to files
        
        Args:
            output_dir: Output directory for results
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # Export trade history
            trades_file = os.path.join(output_dir, "trade_history.csv")
            self.position_manager.export_trade_history(trades_file)
            
            # Export daily performance
            if self.daily_performance:
                daily_file = os.path.join(output_dir, "daily_performance.csv")
                df_daily = pd.DataFrame(self.daily_performance)
                df_daily.to_csv(daily_file, index=False)
                print(f"Exported daily performance to {daily_file}")
            
            # Export performance summary
            if self.backtest_results:
                summary_file = os.path.join(output_dir, "performance_summary.json")
                import json
                with open(summary_file, 'w') as f:
                    json.dump(self.backtest_results, f, indent=2, default=str)
                print(f"Exported performance summary to {summary_file}")
            
            print(f"All results exported to {output_dir}/")
            
        except Exception as e:
            print(f"Error exporting results: {e}")

