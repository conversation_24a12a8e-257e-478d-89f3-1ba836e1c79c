"""
Performance Analysis Module for SPX Overnight Options Strategy
Creates comprehensive analysis and visualizations of backtest results
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
from typing import Dict, List, Optional
import json

class PerformanceAnalyzer:
    """
    Analyzes and visualizes trading strategy performance
    """
    
    def __init__(self, report_dir: str = "report", config=None):
        """
        Initialize performance analyzer

        Args:
            report_dir: Directory containing backtest results
            config: Strategy configuration (optional)
        """
        self.report_dir = report_dir
        self.config = config
        self.trade_history = None
        self.daily_performance = None
        self.performance_summary = None
        
        # Load data
        self.load_data()
        
        # Set up plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
    
    def load_data(self):
        """Load backtest results from files"""
        try:
            # Load trade history
            trade_file = os.path.join(self.report_dir, "trade_history.csv")
            if os.path.exists(trade_file):
                self.trade_history = pd.read_csv(trade_file)
                self.trade_history['entry_date'] = pd.to_datetime(self.trade_history['entry_date'])
                self.trade_history['exit_date'] = pd.to_datetime(self.trade_history['exit_date'])
            
            # Load daily performance
            daily_file = os.path.join(self.report_dir, "daily_performance.csv")
            if os.path.exists(daily_file):
                self.daily_performance = pd.read_csv(daily_file)
                self.daily_performance['date'] = pd.to_datetime(self.daily_performance['date'])
            
            # Load performance summary
            summary_file = os.path.join(self.report_dir, "performance_summary.json")
            if os.path.exists(summary_file):
                with open(summary_file, 'r') as f:
                    data = json.load(f)
                    self.performance_summary = data.get('performance', {})
            
        except Exception as e:
            print(f"Error loading data: {e}")
    
    def calculate_additional_metrics(self) -> Dict:
        """Calculate additional performance metrics"""
        if self.trade_history is None or len(self.trade_history) == 0:
            return {}
        
        metrics = {}
        returns = pd.Series(dtype=float)  # Initialize returns

        # Sharpe ratio (assuming risk-free rate of 5%)
        if len(self.trade_history) > 1:
            returns = self.trade_history['return_percentage'] / 100
            excess_returns = returns - 0.05/252  # Daily risk-free rate
            if excess_returns.std() > 0:
                metrics['sharpe_ratio'] = excess_returns.mean() / excess_returns.std() * np.sqrt(252)
            else:
                metrics['sharpe_ratio'] = 0

        # Sortino ratio (downside deviation)
        if len(returns) > 0:
            negative_returns = returns[returns < 0]
            if len(negative_returns) > 0:
                downside_std = negative_returns.std()
                if downside_std > 0:
                    metrics['sortino_ratio'] = returns.mean() / downside_std * np.sqrt(252)
                else:
                    metrics['sortino_ratio'] = 0
            else:
                metrics['sortino_ratio'] = float('inf')
        else:
            metrics['sortino_ratio'] = 0
        
        # Calmar ratio (annual return / max drawdown)
        if self.performance_summary:
            annual_return = self.performance_summary.get('total_return_percent', 0)
            max_drawdown = self.performance_summary.get('max_drawdown_percent', 1)
            if max_drawdown > 0:
                metrics['calmar_ratio'] = annual_return / max_drawdown
            else:
                metrics['calmar_ratio'] = float('inf')
        
        # Average holding period
        holding_periods = []
        for _, trade in self.trade_history.iterrows():
            entry_date = trade['entry_date']
            exit_date = trade['exit_date']
            holding_period = (exit_date - entry_date).days
            holding_periods.append(holding_period)
        
        metrics['avg_holding_period_days'] = np.mean(holding_periods)
        metrics['max_holding_period_days'] = np.max(holding_periods)
        metrics['min_holding_period_days'] = np.min(holding_periods)
        
        # Consecutive wins/losses
        pnl_signs = np.sign(self.trade_history['realized_pnl'])
        consecutive_wins = 0
        consecutive_losses = 0
        max_consecutive_wins = 0
        max_consecutive_losses = 0
        
        for sign in pnl_signs:
            if sign > 0:
                consecutive_wins += 1
                consecutive_losses = 0
                max_consecutive_wins = max(max_consecutive_wins, consecutive_wins)
            elif sign < 0:
                consecutive_losses += 1
                consecutive_wins = 0
                max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
        
        metrics['max_consecutive_wins'] = max_consecutive_wins
        metrics['max_consecutive_losses'] = max_consecutive_losses
        
        return metrics
    
    def create_equity_curve(self, save_path: str = None) -> str:
        """
        Create equity curve visualization
        
        Args:
            save_path: Path to save the plot
            
        Returns:
            Path to saved plot
        """
        if self.trade_history is None or len(self.trade_history) == 0:
            print("No trade data available for equity curve")
            return None
        
        # Calculate cumulative P&L
        cumulative_pnl = self.trade_history['realized_pnl'].cumsum()
        initial_capital = self.performance_summary.get('initial_capital', 100000)
        equity_curve = initial_capital + cumulative_pnl
        
        # Create plot
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # Equity curve
        ax1.plot(self.trade_history['exit_date'], equity_curve, linewidth=2, color='blue')
        ax1.axhline(y=initial_capital, color='red', linestyle='--', alpha=0.7, label='Initial Capital')
        ax1.set_title('Equity Curve - SPX Overnight Options Strategy', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Portfolio Value ($)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Format y-axis as currency
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
        
        # Drawdown
        peak = equity_curve.expanding().max()
        drawdown = (equity_curve - peak) / peak * 100
        
        ax2.fill_between(self.trade_history['exit_date'], drawdown, 0, 
                        color='red', alpha=0.3, label='Drawdown')
        ax2.plot(self.trade_history['exit_date'], drawdown, color='red', linewidth=1)
        ax2.set_title('Drawdown (%)', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Drawdown (%)', fontsize=12)
        ax2.set_xlabel('Date', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        plt.tight_layout()
        
        # Save plot
        if save_path is None:
            save_path = os.path.join(self.report_dir, "equity_curve.png")
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def create_pnl_distribution(self, save_path: str = None) -> str:
        """
        Create P&L distribution visualization
        
        Args:
            save_path: Path to save the plot
            
        Returns:
            Path to saved plot
        """
        if self.trade_history is None or len(self.trade_history) == 0:
            print("No trade data available for P&L distribution")
            return None
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # P&L histogram
        ax1.hist(self.trade_history['realized_pnl'], bins=20, alpha=0.7, color='blue', edgecolor='black')
        ax1.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        ax1.set_title('P&L Distribution ($)', fontsize=12, fontweight='bold')
        ax1.set_xlabel('P&L ($)')
        ax1.set_ylabel('Frequency')
        ax1.grid(True, alpha=0.3)
        
        # P&L percentage histogram
        ax2.hist(self.trade_history['return_percentage'], bins=20, alpha=0.7, color='green', edgecolor='black')
        ax2.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        ax2.set_title('P&L Distribution (%)', fontsize=12, fontweight='bold')
        ax2.set_xlabel('P&L (%)')
        ax2.set_ylabel('Frequency')
        ax2.grid(True, alpha=0.3)
        
        # Win/Loss by day of week
        self.trade_history['day_of_week'] = self.trade_history['entry_date'].dt.day_name()
        day_pnl = self.trade_history.groupby('day_of_week')['realized_pnl'].sum()
        day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
        day_pnl = day_pnl.reindex([day for day in day_order if day in day_pnl.index])
        
        colors = ['green' if x > 0 else 'red' for x in day_pnl.values]
        ax3.bar(day_pnl.index, day_pnl.values, color=colors, alpha=0.7)
        ax3.set_title('P&L by Day of Week', fontsize=12, fontweight='bold')
        ax3.set_ylabel('Total P&L ($)')
        ax3.grid(True, alpha=0.3)
        plt.setp(ax3.xaxis.get_majorticklabels(), rotation=45)
        
        # Monthly P&L
        self.trade_history['month'] = self.trade_history['entry_date'].dt.to_period('M')
        monthly_pnl = self.trade_history.groupby('month')['realized_pnl'].sum()
        
        colors = ['green' if x > 0 else 'red' for x in monthly_pnl.values]
        ax4.bar(range(len(monthly_pnl)), monthly_pnl.values, color=colors, alpha=0.7)
        ax4.set_title('Monthly P&L', fontsize=12, fontweight='bold')
        ax4.set_ylabel('Total P&L ($)')
        ax4.set_xticks(range(len(monthly_pnl)))
        ax4.set_xticklabels([str(m) for m in monthly_pnl.index], rotation=45)
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save plot
        if save_path is None:
            save_path = os.path.join(self.report_dir, "pnl_distribution.png")
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def create_trade_analysis(self, save_path: str = None) -> str:
        """
        Create trade analysis visualization
        
        Args:
            save_path: Path to save the plot
            
        Returns:
            Path to saved plot
        """
        if self.trade_history is None or len(self.trade_history) == 0:
            print("No trade data available for trade analysis")
            return None
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Trade sequence
        trade_numbers = range(1, len(self.trade_history) + 1)
        colors = ['green' if x > 0 else 'red' for x in self.trade_history['realized_pnl']]

        ax1.bar(trade_numbers, self.trade_history['realized_pnl'], color=colors, alpha=0.7)
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax1.set_title('Individual Trade P&L', fontsize=12, fontweight='bold')
        ax1.set_xlabel('Trade Number')
        ax1.set_ylabel('P&L ($)')
        ax1.grid(True, alpha=0.3)
        
        # Cumulative P&L
        cumulative_pnl = self.trade_history['realized_pnl'].cumsum()
        ax2.plot(trade_numbers, cumulative_pnl, linewidth=2, color='blue', marker='o', markersize=3)
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax2.set_title('Cumulative P&L', fontsize=12, fontweight='bold')
        ax2.set_xlabel('Trade Number')
        ax2.set_ylabel('Cumulative P&L ($)')
        ax2.grid(True, alpha=0.3)
        
        # Strike price vs P&L
        ax3.scatter(self.trade_history['strike_price'], self.trade_history['realized_pnl'],
                   c=colors, alpha=0.7, s=50)
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax3.set_title('Strike Price vs P&L', fontsize=12, fontweight='bold')
        ax3.set_xlabel('Strike Price ($)')
        ax3.set_ylabel('P&L ($)')
        ax3.grid(True, alpha=0.3)
        
        # Entry price vs P&L
        ax4.scatter(self.trade_history['entry_price'], self.trade_history['realized_pnl'],
                   c=colors, alpha=0.7, s=50)
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax4.set_title('Entry Price vs P&L', fontsize=12, fontweight='bold')
        ax4.set_xlabel('Entry Price ($)')
        ax4.set_ylabel('P&L ($)')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save plot
        if save_path is None:
            save_path = os.path.join(self.report_dir, "trade_analysis.png")
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def generate_comprehensive_report(self, output_file: str = None) -> str:
        """
        Generate comprehensive performance report
        
        Args:
            output_file: Path to save the report
            
        Returns:
            Path to saved report
        """
        if output_file is None:
            output_file = os.path.join(self.report_dir, "performance_report.md")
        
        # Calculate additional metrics
        additional_metrics = self.calculate_additional_metrics()
        
        # Create visualizations
        equity_curve_path = self.create_equity_curve()
        pnl_dist_path = self.create_pnl_distribution()
        trade_analysis_path = self.create_trade_analysis()
        
        # Generate report content
        report_content = self._generate_report_content(additional_metrics)
        
        # Write report
        with open(output_file, 'w') as f:
            f.write(report_content)
        
        print(f"Comprehensive report generated: {output_file}")
        return output_file
    
    def _generate_report_content(self, additional_metrics: Dict) -> str:
        """Generate the content for the performance report"""
        
        content = f"""# SPX Overnight Options Strategy - Performance Report

## Executive Summary

This report analyzes the performance of the SPX Overnight Options Strategy, which buys at-the-money (ATM) call options at 3:30 PM and sells them at 10:00 AM the following trading day.

### Strategy Overview
- **Underlying Asset**: S&P 500 Index (SPX)
- **Option Type**: Call Options
- **Entry Time**: 3:30 PM ET
- **Exit Time**: 10:00 AM ET (next trading day)
- **Holding Period**: Overnight (approximately 18.5 hours)

## Performance Summary

"""
        
        if self.performance_summary:
            perf = self.performance_summary
            content += f"""### Key Metrics
- **Initial Capital**: ${perf.get('initial_capital', 0):,.2f}
- **Final Capital**: ${perf.get('final_capital', 0):,.2f}
- **Total Return**: {perf.get('total_return_percent', 0):.2f}%
- **Total P&L**: ${perf.get('total_pnl', 0):,.2f}

### Trading Statistics
- **Total Trades**: {perf.get('total_trades', 0)}
- **Winning Trades**: {perf.get('winning_trades', 0)}
- **Losing Trades**: {perf.get('losing_trades', 0)}
- **Win Rate**: {perf.get('win_rate_percent', 0):.2f}%
- **Profit Factor**: {perf.get('profit_factor', 0):.2f}

### Risk Metrics
- **Average Trade P&L**: ${perf.get('average_trade_pnl', 0):,.2f}
- **Largest Win**: ${perf.get('largest_win', 0):,.2f}
- **Largest Loss**: ${perf.get('largest_loss', 0):,.2f}
- **Maximum Drawdown**: ${perf.get('max_drawdown', 0):,.2f} ({perf.get('max_drawdown_percent', 0):.2f}%)

"""
        
        if additional_metrics:
            content += f"""### Advanced Risk Metrics
- **Sharpe Ratio**: {additional_metrics.get('sharpe_ratio', 0):.3f}
- **Sortino Ratio**: {additional_metrics.get('sortino_ratio', 0):.3f}
- **Calmar Ratio**: {additional_metrics.get('calmar_ratio', 0):.3f}

### Trade Characteristics
- **Average Holding Period**: {additional_metrics.get('avg_holding_period_days', 0):.1f} days
- **Maximum Consecutive Wins**: {additional_metrics.get('max_consecutive_wins', 0)}
- **Maximum Consecutive Losses**: {additional_metrics.get('max_consecutive_losses', 0)}

"""
        
        content += """## Strategy Analysis

### Strengths
1. **Simple Implementation**: The strategy has a clear, mechanical approach that's easy to implement
2. **Defined Risk**: Each trade has a known maximum loss (premium paid)
3. **Overnight Edge**: Attempts to capture overnight price movements in SPX

### Weaknesses
1. **Time Decay**: Options lose value due to theta decay, especially overnight
2. **Volatility Risk**: Strategy is sensitive to implied volatility changes
3. **Gap Risk**: Overnight gaps can cause significant losses

### Market Conditions Impact
The strategy's performance is highly dependent on:
- **Market Direction**: Bull markets generally favor call options
- **Volatility Environment**: High volatility increases option premiums but also risk
- **Interest Rates**: Rising rates can affect option pricing

## Recommendations for Improvement

### 1. Risk Management Enhancements
- Implement stop-loss orders at 50% of premium paid
- Consider position sizing based on volatility
- Add maximum daily loss limits

### 2. Entry/Exit Optimization
- Consider multiple entry times to average into positions
- Implement dynamic exit based on profit targets
- Add volatility filters for trade selection

### 3. Strategy Variations
- Test put options during bearish periods
- Consider straddles or strangles for volatility plays
- Implement calendar spreads to reduce time decay

### 4. Market Regime Adaptation
- Add market trend filters (moving averages, momentum indicators)
- Adjust position sizes based on VIX levels
- Consider seasonal patterns in options trading

## Conclusion

The SPX Overnight Options Strategy shows mixed results in the tested period. While the concept of capturing overnight moves has merit, the current implementation faces challenges from time decay and volatility risk.

**Key Takeaways:**
- The strategy requires careful risk management due to high volatility
- Market conditions significantly impact performance
- Further optimization and testing across different market regimes is recommended

**Next Steps:**
1. Extend backtesting period to include different market conditions
2. Implement suggested improvements and retest
3. Consider paper trading before live implementation
4. Monitor performance metrics continuously

---

*Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        return content

