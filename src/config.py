"""
Configuration Management for SPX Overnight Options Strategy
Centralized configuration system to replace magic numbers and hardcoded values
"""

import os
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class APIConfig:
    """API-related configuration parameters"""
    polygon_api_key: str = field(default_factory=lambda: os.getenv('POLYGON_API_KEY', ''))
    base_url: str = "https://api.polygon.io"
    rate_limit_delay: float = 0.1  # 100ms between requests for free tier
    max_retries: int = 3
    timeout_seconds: int = 30
    
    def __post_init__(self):
        if not self.polygon_api_key:
            raise ValueError("POLYGON_API_KEY environment variable is required")


@dataclass
class StrategyConfig:
    """Core strategy configuration parameters"""
    # Asset and contract settings
    underlying_symbol: str = field(default_factory=lambda: os.getenv('UNDERLYING_SYMBOL', 'SPX'))
    contract_type: str = field(default_factory=lambda: os.getenv('CONTRACT_TYPE', 'call'))
    strike_offset: float = field(default_factory=lambda: float(os.getenv('STRIKE_OFFSET', '50')))  # Points OTM

    # Trading times (24-hour format)
    entry_time: str = field(default_factory=lambda: os.getenv('ENTRY_TIME', '12:00'))  # 12:00 PM ET (Noon)
    exit_time: str = field(default_factory=lambda: os.getenv('EXIT_TIME', '12:00'))    # 12:00 PM ET (Noon next day)
    
    # Backtesting period
    start_date: str = field(default_factory=lambda: os.getenv('START_DATE', '2024-01-01'))
    end_date: str = field(default_factory=lambda: os.getenv('END_DATE', '2024-01-31'))
    
    # Capital settings
    initial_capital: float = field(default_factory=lambda: float(os.getenv('INITIAL_CAPITAL', '100000')))
    
    # Expiration settings
    min_days_to_expiration: int = 1
    max_days_to_expiration: int = 90
    preferred_days_to_expiration: int = 60


@dataclass
class RiskManagementConfig:
    """Risk management configuration parameters"""
    # Position sizing
    max_position_size: float = 0.35  # 35% of capital per trade
    min_position_size: float = 0.01  # 1% minimum position size
    
    # Loss limits
    max_daily_loss: float = 0.05     # 5% daily loss limit
    max_total_loss: float = 0.20     # 20% total portfolio loss limit
    
    # Stop loss settings
    enable_stop_loss: bool = field(default_factory=lambda: os.getenv('ENABLE_STOP_LOSS', 'false').lower() == 'true')
    stop_loss_percentage: float = 0.50  # Stop loss at 50% of premium paid
    
    # Contract settings
    contract_multiplier: int = 100  # SPX options contract multiplier


@dataclass
class VolatilityFilterConfig:
    """Volatility regime filter configuration"""
    # Filter enable/disable
    enable_vix_filter: bool = field(default_factory=lambda: os.getenv('ENABLE_VIX_FILTER', 'true').lower() == 'true')
    
    # VIX regime thresholds
    vix_low_threshold: float = 12.0      # Below this is low volatility
    vix_medium_threshold: float = 20.0   # Below this is medium volatility  
    vix_high_threshold: float = 30.0     # Below this is high volatility
    # Above high_threshold is extreme volatility
    
    # Trading permissions by regime
    trade_in_low_vol: bool = field(default_factory=lambda: os.getenv('TRADE_IN_LOW_VOL', 'true').lower() == 'true')
    trade_in_medium_vol: bool = field(default_factory=lambda: os.getenv('TRADE_IN_MEDIUM_VOL', 'true').lower() == 'true')
    trade_in_high_vol: bool = field(default_factory=lambda: os.getenv('TRADE_IN_HIGH_VOL', 'false').lower() == 'true')
    trade_in_extreme_vol: bool = field(default_factory=lambda: os.getenv('TRADE_IN_EXTREME_VOL', 'false').lower() == 'true')
    
    # VIX data settings
    vix_symbol: str = "I:VIX"
    vix_lookback_days: int = 5  # Days to look back for VIX data if current day unavailable


@dataclass
class PricingConfig:
    """Option pricing configuration parameters"""
    # Black-Scholes parameters
    risk_free_rate: float = 0.05        # 5% risk-free rate assumption
    default_volatility: float = 0.20    # 20% default volatility
    
    # Synthetic option generation
    strike_range: float = 0.10          # Generate strikes within 10% of underlying price
    strike_increment: float = 25.0      # $25 strike increments for SPX
    
    # Pricing validation
    min_option_price: float = 0.01      # Minimum valid option price
    max_option_price: float = 1000.0    # Maximum valid option price


@dataclass
class DataConfig:
    """Data handling configuration"""
    # Timezone settings
    market_timezone: str = "US/Eastern"
    
    # Data validation
    max_price_change: float = 0.20      # 20% maximum price change validation
    min_underlying_price: float = 1000.0  # Minimum valid SPX price
    max_underlying_price: float = 10000.0 # Maximum valid SPX price
    
    # Caching settings
    enable_data_caching: bool = True
    cache_expiry_hours: int = 24


@dataclass
class ReportingConfig:
    """Reporting and analysis configuration"""
    # Output directories
    report_directory: str = "report"
    data_directory: str = "data"
    
    # Report settings
    generate_pdf: bool = True
    generate_plots: bool = True
    plot_dpi: int = 300
    plot_style: str = "seaborn-v0_8"
    
    # Performance metrics
    benchmark_symbol: str = "SPY"  # Benchmark for comparison
    risk_free_rate_annual: float = 0.05  # For Sharpe ratio calculation


@dataclass
class StrategyConfiguration:
    """Main configuration container for the entire strategy"""
    api: APIConfig = field(default_factory=APIConfig)
    strategy: StrategyConfig = field(default_factory=StrategyConfig)
    risk_management: RiskManagementConfig = field(default_factory=RiskManagementConfig)
    volatility_filter: VolatilityFilterConfig = field(default_factory=VolatilityFilterConfig)
    pricing: PricingConfig = field(default_factory=PricingConfig)
    data: DataConfig = field(default_factory=DataConfig)
    reporting: ReportingConfig = field(default_factory=ReportingConfig)
    
    @classmethod
    def load_from_env(cls) -> 'StrategyConfiguration':
        """Load configuration from environment variables and defaults"""
        return cls()
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []
        
        # Validate API configuration
        if not self.api.polygon_api_key:
            errors.append("Polygon API key is required")
        
        # Validate strategy parameters
        if self.strategy.initial_capital <= 0:
            errors.append("Initial capital must be positive")
        
        if self.risk_management.max_position_size <= 0 or self.risk_management.max_position_size > 1:
            errors.append("Max position size must be between 0 and 1")
        
        # Validate volatility thresholds
        vf = self.volatility_filter
        if not (vf.vix_low_threshold < vf.vix_medium_threshold < vf.vix_high_threshold):
            errors.append("VIX thresholds must be in ascending order")
        
        # Validate pricing parameters
        if self.pricing.risk_free_rate < 0:
            errors.append("Risk-free rate cannot be negative")
        
        if self.pricing.default_volatility <= 0:
            errors.append("Default volatility must be positive")
        
        return errors
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary for serialization"""
        return {
            'api': self.api.__dict__,
            'strategy': self.strategy.__dict__,
            'risk_management': self.risk_management.__dict__,
            'volatility_filter': self.volatility_filter.__dict__,
            'pricing': self.pricing.__dict__,
            'data': self.data.__dict__,
            'reporting': self.reporting.__dict__
        }


# Global configuration instance
config = StrategyConfiguration.load_from_env()
