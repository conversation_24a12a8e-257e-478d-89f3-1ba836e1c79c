# SPX Overnight Strategy - API Documentation

## Table of Contents

1. [Configuration System](#configuration-system)
2. [Data Retrieval](#data-retrieval)
3. [Position Management](#position-management)
4. [Option Pricing](#option-pricing)
5. [Volatility Filtering](#volatility-filtering)
6. [Performance Analysis](#performance-analysis)
7. [Main Strategy](#main-strategy)

## Configuration System

### ConfigurationManager

Main class for loading and managing configuration.

```python
class ConfigurationManager:
    def __init__(self, config_file: Optional[str] = None)
    def load_configuration(self) -> StrategyConfiguration
    def save_configuration(self, output_file: str) -> None
    def print_configuration(self) -> None
    def create_sample_config_file(self, output_file: str) -> None
```

#### Usage Example:
```python
from config_manager import ConfigurationManager

manager = ConfigurationManager("config.json")
config = manager.load_configuration()
```

### Configuration Classes

#### StrategyConfiguration
Main configuration container with all settings.

```python
@dataclass
class StrategyConfiguration:
    api: APIConfig
    strategy: StrategyConfig
    risk_management: RiskManagementConfig
    volatility_filter: VolatilityFilterConfig
    pricing: PricingConfig
    data: DataConfig
    reporting: ReportingConfig
```

#### Key Configuration Sections:

**APIConfig**
- `polygon_api_key`: Polygon.io API key
- `base_url`: API base URL
- `rate_limit_delay`: Delay between requests
- `max_retries`: Maximum retry attempts
- `timeout_seconds`: Request timeout

**StrategyConfig**
- `underlying_symbol`: Asset symbol (default: SPX)
- `contract_type`: Option type (call/put)
- `entry_time`: Entry time (15:30)
- `exit_time`: Exit time (10:00)
- `initial_capital`: Starting capital

**RiskManagementConfig**
- `max_position_size`: Maximum position size (0.35)
- `max_daily_loss`: Daily loss limit (0.05)
- `enable_stop_loss`: Stop loss enabled
- `stop_loss_percentage`: Stop loss threshold (0.50)

## Data Retrieval

### PolygonDataRetriever

Enhanced data retrieval with error handling and validation.

```python
class PolygonDataRetriever:
    def __init__(self, config: Optional[StrategyConfiguration] = None)
    def get_underlying_price(self, date: str, symbol: Optional[str] = None) -> Optional[float]
    def get_options_chain(self, underlying: Optional[str] = None, 
                         expiration_date: Optional[str] = None,
                         contract_type: Optional[str] = None) -> List[OptionsContract]
    def find_atm_option(self, underlying_price: float, 
                       options_chain: List[OptionsContract]) -> Optional[OptionsContract]
    def get_vix_data(self, date: str) -> Optional[float]
```

### Data Structures

#### OptionsContract
```python
@dataclass
class OptionsContract:
    ticker: str
    underlying_symbol: str
    contract_type: str
    strike_price: float
    expiration_date: str
    shares_per_contract: int = 100
    last_price: Optional[float] = None
    bid: Optional[float] = None
    ask: Optional[float] = None
    
    def is_valid(self) -> bool
    def to_dict(self) -> Dict
```

#### MarketDataPoint
```python
@dataclass
class MarketDataPoint:
    timestamp: datetime
    price: float
    volume: Optional[int] = None
    symbol: str = ""
    
    def is_valid(self, min_price: float = 0.01, max_price: float = 10000.0) -> bool
```

## Position Management

### PositionManager

Comprehensive portfolio management with risk controls.

```python
class PositionManager:
    def __init__(self, config: Optional[StrategyConfiguration] = None)
    def calculate_position_size(self, option_price: float, underlying_price: float) -> int
    def can_open_position(self, option_price: float) -> Tuple[bool, str]
    def open_position(self, ticker: str, contract_type: str, strike_price: float,
                     expiration_date: str, entry_date: str, entry_time: str,
                     entry_price: float, underlying_price: float) -> bool
    def close_position(self, position_index: int, exit_date: str, exit_time: str,
                      exit_price: float, underlying_price: float) -> bool
    def get_portfolio_summary(self) -> Dict
    def calculate_trade_metrics(self) -> TradeMetrics
    def get_risk_metrics(self, current_prices: Dict[str, float]) -> RiskMetrics
```

### Position Classes

#### OptionsPosition
```python
@dataclass
class OptionsPosition:
    ticker: str
    contract_type: str
    strike_price: float
    expiration_date: str
    entry_date: str
    entry_time: str
    entry_price: float
    quantity: int
    underlying_price_entry: float
    
    def calculate_pnl(self, current_price: Optional[float] = None) -> float
    def get_position_value(self, current_price: Optional[float] = None) -> float
    def get_return_percentage(self) -> float
    def is_profitable(self) -> bool
```

#### TradeMetrics
```python
class TradeMetrics(NamedTuple):
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    max_drawdown: float
```

## Option Pricing

### BlackScholesCalculator

Enhanced Black-Scholes implementation with Greeks.

```python
class BlackScholesCalculator:
    def __init__(self, config: Optional[StrategyConfiguration] = None)
    def calculate_time_to_expiration(self, current_date: str, expiration_date: str) -> float
    def black_scholes_call(self, S: float, K: float, T: float, r: float, sigma: float) -> float
    def black_scholes_put(self, S: float, K: float, T: float, r: float, sigma: float) -> float
    def calculate_option_price(self, underlying_price: float, strike_price: float,
                             current_date: str, expiration_date: str,
                             option_type: str = "call") -> float
    def calculate_greeks(self, S: float, K: float, T: float, r: float, sigma: float,
                        option_type: str = "call") -> Dict[str, float]
```

### SyntheticOptionsGenerator

Generate synthetic options data for backtesting.

```python
class SyntheticOptionsGenerator:
    def __init__(self, config: Optional[StrategyConfiguration] = None)
    def generate_atm_option(self, underlying_price: float, current_date: str,
                           expiration_date: str, option_type: str = "call") -> Optional[Dict]
    def generate_options_chain(self, underlying_price: float, current_date: str,
                              expiration_date: str, option_type: str = "call",
                              num_strikes: int = 10) -> List[Dict]
```

## Volatility Filtering

### VolatilityRegimeFilter

VIX-based volatility regime analysis.

```python
class VolatilityRegimeFilter:
    def __init__(self, data_retriever, config: Optional[StrategyConfiguration] = None)
    def get_vix_value(self, date: str) -> Optional[float]
    def determine_volatility_regime(self, date: str) -> VolatilityData
    def should_trade(self, date: str) -> TradingDecision
    def get_regime_statistics(self, start_date: str, end_date: str) -> Dict[str, int]
```

### Volatility Classes

#### VolatilityRegime
```python
class VolatilityRegime(Enum):
    LOW = "low_vol"
    MEDIUM = "medium_vol"
    HIGH = "high_vol"
    EXTREME = "extreme_vol"
    UNKNOWN = "unknown"
```

#### VolatilityData
```python
class VolatilityData(NamedTuple):
    vix_current: float
    regime: VolatilityRegime
    threshold_low: float
    threshold_medium: float
    threshold_high: float
    date: str
```

#### TradingDecision
```python
class TradingDecision(NamedTuple):
    should_trade: bool
    reason: str
    regime: VolatilityRegime
    vix_value: float
```

## Performance Analysis

### PerformanceAnalyzer

Comprehensive performance analysis and reporting.

```python
class PerformanceAnalyzer:
    def __init__(self, report_dir: str, config: Optional[StrategyConfiguration] = None)
    def load_trade_history(self) -> pd.DataFrame
    def calculate_performance_metrics(self) -> Dict[str, float]
    def create_equity_curve(self) -> str
    def create_pnl_distribution(self) -> str
    def generate_comprehensive_report(self, output_file: Optional[str] = None) -> str
    def generate_pdf_report(self, output_file: Optional[str] = None) -> str
```

## Main Strategy

### SPXOvernightStrategy

Main strategy implementation.

```python
class SPXOvernightStrategy:
    def __init__(self, config: Optional[StrategyConfiguration] = None)
    def find_suitable_expiration(self, trade_date: str) -> Optional[str]
    def execute_entry_signal(self, trade_date: str) -> bool
    def execute_exit_signal(self, trade_date: str) -> bool
    def run_backtest(self, start_date: str, end_date: str) -> bool
    def get_performance_summary(self) -> Dict
    def export_results(self, output_dir: str) -> None
```

## Error Handling

### Custom Exceptions

```python
class DataRetrievalError(Exception): pass
class PositionError(Exception): pass
class OptionPricingError(Exception): pass
```

### Error Handling Pattern

All methods follow consistent error handling:

```python
try:
    # Operation
    result = perform_operation()
    return result
except SpecificError:
    # Handle specific error
    raise
except Exception as e:
    # Log and re-raise with context
    logger.error(f"Operation failed: {e}")
    raise CustomError(f"Operation failed: {e}")
```

## Usage Examples

### Basic Usage

```python
from config_manager import load_config
from overnight_strategy import SPXOvernightStrategy

# Load configuration
config = load_config()

# Initialize strategy
strategy = SPXOvernightStrategy(config=config)

# Run backtest
results = strategy.run_backtest("2024-01-01", "2024-12-31")

# Get performance summary
summary = strategy.get_performance_summary()
print(f"Total Return: {summary['total_return_pct']:.2f}%")
```

### Advanced Configuration

```python
from config_manager import ConfigurationManager

# Load custom configuration
manager = ConfigurationManager("custom_config.json")
config = manager.load_configuration()

# Modify configuration
config.risk_management.max_position_size = 0.25
config.volatility_filter.trade_in_high_vol = False

# Validate configuration
errors = config.validate()
if errors:
    print("Configuration errors:", errors)
```

### Custom Analysis

```python
from performance_analysis import PerformanceAnalyzer

# Initialize analyzer
analyzer = PerformanceAnalyzer("report", config=config)

# Generate custom report
report_path = analyzer.generate_comprehensive_report()

# Export detailed metrics
analyzer.export_detailed_metrics("metrics.json")
```
